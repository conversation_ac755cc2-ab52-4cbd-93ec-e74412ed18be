<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة تحميل الدرجات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        .fix-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .fix-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .fix-section h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .fix-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #219a52;
        }
        .btn-warning {
            background: #f39c12;
        }
        .btn-warning:hover {
            background: #e67e22;
        }
        .btn-danger {
            background: #e74c3c;
        }
        .btn-danger:hover {
            background: #c0392b;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <h1>إصلاح مشكلة تحميل الدرجات</h1>
        
        <div class="fix-section">
            <h3>1. فحص حالة قاعدة البيانات</h3>
            <button onclick="checkDatabaseStatus()">فحص قاعدة البيانات</button>
            <div id="db-status" class="fix-result"></div>
        </div>

        <div class="fix-section">
            <h3>2. إضافة بيانات تجريبية</h3>
            <button class="btn-success" onclick="addSampleStudents()">إضافة طلاب تجريبيين</button>
            <button class="btn-warning" onclick="clearAllData()">مسح جميع البيانات</button>
            <div id="sample-data-result" class="fix-result"></div>
        </div>

        <div class="fix-section">
            <h3>3. اختبار تحميل الدرجات</h3>
            <button onclick="testGradeLoading()">اختبار تحميل جدول الدرجات</button>
            <div id="grade-test-result" class="fix-result"></div>
        </div>

        <div class="fix-section">
            <h3>4. إعادة تهيئة النظام</h3>
            <button class="btn-danger" onclick="resetSystem()">إعادة تهيئة كاملة</button>
            <div id="reset-result" class="fix-result"></div>
        </div>

        <div class="fix-section">
            <h3>5. فتح التطبيق الرئيسي</h3>
            <button class="btn-success" onclick="openMainApp()">فتح التطبيق</button>
            <div id="app-result" class="fix-result"></div>
        </div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="js/database.js"></script>
    <script>
        let dbManager;
        
        window.addEventListener('DOMContentLoaded', async () => {
            try {
                dbManager = new DatabaseManager();
                await new Promise(resolve => setTimeout(resolve, 2000));
                document.getElementById('db-status').textContent = '✓ قاعدة البيانات جاهزة للاستخدام';
                document.getElementById('db-status').className = 'fix-result success';
            } catch (error) {
                document.getElementById('db-status').textContent = '✗ خطأ في تحميل قاعدة البيانات: ' + error.message;
                document.getElementById('db-status').className = 'fix-result error';
            }
        });

        async function checkDatabaseStatus() {
            const result = document.getElementById('db-status');
            try {
                if (!dbManager || !dbManager.db) {
                    result.textContent = '✗ قاعدة البيانات غير متاحة';
                    result.className = 'fix-result error';
                    return;
                }
                
                const students = await dbManager.getStudents();
                const grades = await dbManager.getGrades();
                const settings = await dbManager.getAllSettings();
                
                result.textContent = `✓ قاعدة البيانات متاحة\nعدد الطلاب: ${students.length}\nعدد الدرجات: ${grades.length}\nعدد الإعدادات: ${settings.length}`;
                result.className = 'fix-result success';
            } catch (error) {
                result.textContent = '✗ خطأ: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        async function addSampleStudents() {
            const result = document.getElementById('sample-data-result');
            try {
                const sampleStudents = [
                    { student_number: '1001', name: 'أحمد محمد علي', grade: '1', section: 'أ' },
                    { student_number: '1002', name: 'فاطمة أحمد سالم', grade: '1', section: 'أ' },
                    { student_number: '1003', name: 'محمد علي حسن', grade: '1', section: 'أ' },
                    { student_number: '1004', name: 'نور الدين خالد', grade: '1', section: 'ب' },
                    { student_number: '1005', name: 'سارة عبدالله', grade: '1', section: 'ب' },
                    { student_number: '2001', name: 'عمر يوسف', grade: '2', section: 'أ' },
                    { student_number: '2002', name: 'ليلى حسام', grade: '2', section: 'أ' },
                    { student_number: '2003', name: 'كريم محمود', grade: '2', section: 'ب' },
                ];

                let successCount = 0;
                let errorCount = 0;

                for (const student of sampleStudents) {
                    try {
                        await dbManager.addStudent(student);
                        successCount++;
                    } catch (error) {
                        errorCount++;
                        console.error('خطأ في إضافة الطالب:', error);
                    }
                }

                result.textContent = `✓ تم إضافة ${successCount} طالب بنجاح\n${errorCount > 0 ? `فشل في إضافة ${errorCount} طالب` : ''}`;
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في إضافة البيانات التجريبية: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        async function clearAllData() {
            if (!confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                return;
            }

            const result = document.getElementById('sample-data-result');
            try {
                // مسح جميع الطلاب
                const students = await dbManager.getStudents();
                for (const student of students) {
                    await dbManager.deleteStudent(student.id);
                }

                // مسح جميع الدرجات
                const grades = await dbManager.getGrades();
                for (const grade of grades) {
                    // يمكن إضافة دالة deleteGrade إذا لزم الأمر
                }

                result.textContent = '✓ تم مسح جميع البيانات بنجاح';
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في مسح البيانات: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        async function testGradeLoading() {
            const result = document.getElementById('grade-test-result');
            try {
                // محاكاة تحميل جدول الدرجات
                const academicYear = '2024-2025';
                const semester = '1';
                const grade = '1';
                const section = 'أ';

                // تحميل الطلاب
                const students = await dbManager.getStudents({ grade: grade, section: section });
                
                if (students.length === 0) {
                    result.textContent = '⚠ لا توجد طلاب في الصف الأول الشعبة أ\nيرجى إضافة بيانات تجريبية أولاً';
                    result.className = 'fix-result warning';
                    return;
                }

                // تحميل الدرجات الموجودة
                const existingGrades = await dbManager.getGrades({
                    academic_year: academicYear,
                    semester: semester,
                    grade_level: grade
                });

                result.textContent = `✓ نجح اختبار تحميل جدول الدرجات!\n\nتفاصيل:\n- العام الدراسي: ${academicYear}\n- الفصل: ${semester}\n- الصف: ${grade}\n- الشعبة: ${section}\n- عدد الطلاب: ${students.length}\n- عدد الدرجات الموجودة: ${existingGrades.length}\n\nالطلاب:\n${students.map(s => `${s.student_number} - ${s.name}`).join('\n')}`;
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في اختبار تحميل الدرجات: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        async function resetSystem() {
            if (!confirm('هل أنت متأكد من إعادة تهيئة النظام بالكامل؟ سيتم مسح جميع البيانات وإعادة إنشائها.')) {
                return;
            }

            const result = document.getElementById('reset-result');
            try {
                // مسح البيانات الموجودة
                await clearAllData();
                
                // إضافة البيانات التجريبية
                await addSampleStudents();
                
                // إعادة تحميل الإعدادات الافتراضية
                await dbManager.setSetting('school_name', 'مدرسة تقنية المعلومات');
                await dbManager.setSetting('current_academic_year', '2024-2025');
                await dbManager.setSetting('ministry_name', 'وزارة التربية والتعليم');
                await dbManager.setSetting('directorate_name', 'مديرية التربية والتعليم');
                
                result.textContent = '✓ تم إعادة تهيئة النظام بنجاح\nتم إضافة بيانات تجريبية جديدة';
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في إعادة تهيئة النظام: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        function openMainApp() {
            const result = document.getElementById('app-result');
            try {
                window.open('index.html', '_blank');
                result.textContent = '✓ تم فتح التطبيق الرئيسي في نافذة جديدة';
                result.className = 'fix-result success';
            } catch (error) {
                result.textContent = '✗ خطأ في فتح التطبيق: ' + error.message;
                result.className = 'fix-result error';
            }
        }
    </script>
</body>
</html>
