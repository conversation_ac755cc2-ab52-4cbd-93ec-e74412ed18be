/* استمارة تقويم تقنية المعلومات - الأنماط الرئيسية */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f6fa;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem 0;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-section i {
    font-size: 2rem;
}

.logo-section h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

.header-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
    font-size: 0.9rem;
}

/* Navigation */
.main-nav {
    background: white;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0;
}

.nav-menu li {
    flex: 1;
}

.nav-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 15px 20px;
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    border-bottom: 3px solid transparent;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--light-color);
    border-bottom-color: var(--secondary-color);
    color: var(--secondary-color);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Dashboard */
.dashboard-header {
    text-align: center;
    margin-bottom: 2rem;
}

.dashboard-header h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.card:nth-child(1) .card-icon { background: var(--primary-color); }
.card:nth-child(2) .card-icon { background: var(--success-color); }
.card:nth-child(3) .card-icon { background: var(--warning-color); }
.card:nth-child(4) .card-icon { background: var(--info-color); }

.card-content h3 {
    font-size: 1rem;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.card-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Quick Actions */
.quick-actions {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.quick-actions h3 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    font-family: inherit;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #1a252f;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #2980b9;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #219a52;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: #138496;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.section-header h2 {
    font-size: 2rem;
    color: var(--primary-color);
}

.section-actions {
    display: flex;
    gap: 1rem;
}

/* Filters */
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    color: var(--dark-color);
}

.filter-group select,
.filter-group input {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
}

/* Tables */
.students-table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--primary-color);
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

/* Grade Selection */
.grade-selection {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.selection-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.selection-group label {
    font-weight: 500;
    color: var(--dark-color);
}

.selection-group select {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
}

/* Grade Sheet */
.grade-sheet-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-top: 1.5rem;
}

/* Reports Grid */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.report-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
}

.report-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.report-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.setting-group {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.setting-group h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-item label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.setting-item input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
}

.setting-item input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

.setting-item button {
    margin-top: 0.5rem;
    width: 100%;
}

/* Footer */
.app-footer {
    background: var(--primary-color);
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
}

/* Modal */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .nav-menu {
        flex-direction: column;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .filters-section,
    .grade-selection {
        flex-direction: column;
        align-items: stretch;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .section-actions {
        justify-content: center;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.content-section.active {
    animation: fadeIn 0.3s ease;
}

/* Forms */
.student-form,
.criteria-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 500px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--dark-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
}

/* Import Section */
.import-section {
    padding: 1.5rem;
}

.import-instructions {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
}

.import-instructions h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.import-instructions ul {
    margin: 0;
    padding-right: 1.5rem;
}

.import-instructions li {
    margin-bottom: 0.5rem;
}

.file-upload {
    text-align: center;
    margin: 1.5rem 0;
}

.file-input {
    display: none;
}

.file-label {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    background: var(--secondary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
    font-weight: 500;
}

.file-label:hover {
    background: #2980b9;
}

.import-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Grade Sheet Styles */
.grade-sheet-header {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: 2px solid var(--border-color);
}

.grade-sheet-header h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.grade-sheet-info {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: #666;
}

.grade-table-container {
    overflow-x: auto;
    background: white;
}

.grade-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.grade-table th,
.grade-table td {
    padding: 8px 6px;
    text-align: center;
    border: 1px solid var(--border-color);
    vertical-align: middle;
}

.grade-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.grade-table tbody tr:hover {
    background: #f8f9fa;
}

.grade-input {
    width: 70px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 0.85rem;
}

.grade-input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 3px rgba(52, 152, 219, 0.3);
}

.total-cell,
.level-cell,
.description-cell {
    font-weight: bold;
    background: #f8f9fa;
}

.grade-sheet-actions {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    display: flex;
    gap: 1rem;
    justify-content: center;
    border-top: 1px solid var(--border-color);
}

/* Report Content */
.report-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-top: 1.5rem;
    overflow: hidden;
}

.statistics-section,
.grade-distribution-section,
.detailed-grades-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.statistics-section:last-child,
.grade-distribution-section:last-child,
.detailed-grades-section:last-child {
    border-bottom: none;
}

.statistics-section h3,
.grade-distribution-section h3,
.detailed-grades-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

/* Button Sizes */
.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.125rem;
}

/* Final Results */
.final-results-filters {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    align-items: end;
}

.final-results-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.no-results-message {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-results-message i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--info-color);
}

.final-results-header {
    background: var(--primary-color);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.final-results-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.final-results-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

.final-results-table-container {
    overflow-x: auto;
    max-height: 70vh;
}

.final-results-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.final-results-table th,
.final-results-table td {
    padding: 10px 8px;
    text-align: center;
    border: 1px solid var(--border-color);
    vertical-align: middle;
}

.final-results-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.final-results-table tbody tr:hover {
    background: #f8f9fa;
}

.final-results-table .student-info {
    text-align: right;
    font-weight: 500;
}

.final-results-table .semester-total {
    font-weight: bold;
    background: #f8f9fa;
}

.final-results-table .final-total {
    font-weight: bold;
    background: var(--primary-color);
    color: white;
}

.final-results-table .average-cell {
    font-weight: bold;
    background: #e3f2fd;
}

.final-results-table .percentage-cell {
    font-weight: bold;
}

.final-results-table .grade-cell {
    font-weight: bold;
    font-size: 1rem;
}

/* Final Results Grade Colors */
.final-grade-a { background: #c8e6c9 !important; color: #2e7d32; }
.final-grade-b { background: #bbdefb !important; color: #1565c0; }
.final-grade-c { background: #fff3e0 !important; color: #ef6c00; }
.final-grade-d { background: #ffecb3 !important; color: #f57f17; }
.final-grade-f { background: #ffcdd2 !important; color: #c62828; }

.final-results-summary {
    padding: 1.5rem;
    border-top: 2px solid var(--border-color);
    background: #f8f9fa;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.summary-stat {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-stat h4 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.summary-stat .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--secondary-color);
}

.grade-distribution-summary {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.grade-summary-item {
    text-align: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: bold;
    min-width: 80px;
}

.grade-summary-a { background: #c8e6c9; color: #2e7d32; }
.grade-summary-b { background: #bbdefb; color: #1565c0; }
.grade-summary-c { background: #fff3e0; color: #ef6c00; }
.grade-summary-d { background: #ffecb3; color: #f57f17; }
.grade-summary-f { background: #ffcdd2; color: #c62828; }

/* البيانات غير المكتملة */
.incomplete-data {
    background: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
}

.incomplete-data::after {
    content: " ⚠️";
    color: #ffc107;
    font-weight: bold;
}

/* تحسينات إضافية للنتائج النهائية */
.rank-cell {
    font-weight: bold;
    background: #e3f2fd !important;
    color: #1565c0;
}

.student-info {
    text-align: right !important;
    font-weight: 500;
    padding-right: 10px !important;
}

/* رسائل التحذير */
.warning-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
    color: #856404;
}

.warning-message i {
    margin-left: 0.5rem;
    color: #ffc107;
}

/* Report Actions - أزرار التقارير المحسنة */
.report-actions {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: var(--shadow);
}

.print-options-section,
.export-options-section,
.preview-options-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.print-options-section:last-child,
.export-options-section:last-child,
.preview-options-section:last-child {
    margin-bottom: 0;
}

.print-options-section h4,
.export-options-section h4,
.preview-options-section h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-color);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.print-buttons-grid,
.export-buttons-grid,
.preview-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.print-btn {
    padding: 12px 20px;
    font-size: 0.95rem;
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-height: 50px;
}

.print-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.print-btn i {
    font-size: 1.1rem;
}

/* Print Settings Modal */
.print-settings-content {
    max-width: 600px;
    padding: 1.5rem;
}

.setting-group {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: #f8f9fa;
}

.setting-group h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1rem;
}

.setting-item {
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.setting-item label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.setting-item input[type="radio"],
.setting-item input[type="checkbox"] {
    margin-left: 0.5rem;
}

.setting-item select {
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
}

/* Print Mode Styles */
.print-mode {
    background: white !important;
}

.print-mode .main-nav,
.print-mode .app-footer,
.print-mode .section-actions,
.print-mode .filters-section,
.print-mode .no-print {
    display: none !important;
}

.print-mode .main-content {
    padding: 0 !important;
}

.print-mode .container {
    max-width: none !important;
    padding: 2cm !important;
}

/* Print Metadata */
.print-metadata {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: 2px solid var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: center;
}

.print-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 500;
}

/* Enhanced Button Styles */
.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
}

.btn-outline-info {
    background: transparent;
    color: var(--info-color);
    border: 2px solid var(--info-color);
}

.btn-outline-info:hover {
    background: var(--info-color);
    color: white;
}

/* Dropdown Styles */
.dropdown-group {
    position: relative;
    display: inline-block;
}

.dropdown-toggle::after {
    content: " ▼";
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 200px;
    padding: 0.5rem 0;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    text-align: right;
    cursor: pointer;
    transition: var(--transition);
    font-family: inherit;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* تحسينات أزرار الطباعة */
.dropdown-group {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    position: relative;
}

.dropdown-toggle::after {
    content: '\f0d7';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 8px;
    transition: transform 0.3s ease;
}

.dropdown-toggle.active::after {
    transform: rotate(180deg);
}

/* تحسينات ألوان المستويات للطباعة */
.grade-excellent {
    background-color: #d4edda !important;
    color: #155724 !important;
    border-color: #c3e6cb !important;
}

.grade-very-good {
    background-color: #d1ecf1 !important;
    color: #0c5460 !important;
    border-color: #bee5eb !important;
}

.grade-good {
    background-color: #fff3cd !important;
    color: #856404 !important;
    border-color: #ffeaa7 !important;
}

.grade-acceptable {
    background-color: #ffeaa7 !important;
    color: #6c5ce7 !important;
    border-color: #fdcb6e !important;
}

.grade-weak {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    border-color: #f5c6cb !important;
}

/* تنسيقات بطاقات التقارير */
.report-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.report-card h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.report-card p {
    color: var(--text-color);
    margin-bottom: 15px;
    line-height: 1.5;
}

.report-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.report-actions .btn {
    flex: 1;
    min-width: 120px;
}

.report-actions .dropdown-group {
    flex: 0 0 auto;
}

/* تحسينات القوائم المنسدلة للتقارير */
.reports-grid .dropdown-group {
    position: relative;
}

.reports-grid .dropdown-menu {
    min-width: 150px;
    right: 0;
    left: auto;
}

.reports-grid .dropdown-item {
    padding: 8px 12px;
    font-size: 0.9rem;
}

/* تنسيقات خاصة لقسم التقارير */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* لوحة تحكم التقارير المحسنة */
.reports-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    color: white;
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    flex-shrink: 0;
}

.stat-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #2c3e50;
    line-height: 1;
}

/* فلاتر التقارير */
.reports-filters {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 150px;
}

.filter-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.filter-group select {
    padding: 10px 12px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* بطاقات التقارير المحسنة */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 25px;
}

.report-card.enhanced {
    background: white;
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: 1px solid #f0f0f0;
}

.report-card.enhanced:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.report-header {
    padding: 25px 25px 15px 25px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.report-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
    background: linear-gradient(135deg, var(--primary-color), #667eea);
}

.report-title h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #2c3e50;
    font-weight: 600;
}

.report-title p {
    margin: 0;
    color: #7f8c8d;
    font-size: 14px;
    line-height: 1.5;
}

.report-features {
    padding: 0 25px 15px 25px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.feature-tag {
    background: #f8f9fa;
    color: #495057;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.feature-tag i {
    font-size: 10px;
}

.report-actions {
    padding: 20px 25px 25px 25px;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.report-actions .btn {
    flex: 1;
    min-width: 120px;
    font-size: 14px;
    padding: 10px 15px;
}

.report-actions .dropdown-group {
    flex: 0 0 auto;
}

/* أزرار ملونة إضافية */
.btn-purple {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.btn-purple:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    color: white;
}

.btn-outline-purple {
    border: 2px solid #667eea;
    color: #667eea;
    background: transparent;
}

.btn-outline-purple:hover {
    background: #667eea;
    color: white;
}

.btn-dark {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    border: none;
}

.btn-dark:hover {
    background: linear-gradient(135deg, #1a252f, #2c3e50);
    color: white;
}

.btn-outline-dark {
    border: 2px solid #2c3e50;
    color: #2c3e50;
    background: transparent;
}

.btn-outline-dark:hover {
    background: #2c3e50;
    color: white;
}

/* تحسينات للطباعة في التقارير */
@media print {
    .report-actions,
    .section-actions,
    .reports-filters,
    .reports-dashboard {
        display: none !important;
    }

    .report-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .stat-card {
        padding: 15px;
        gap: 10px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .stat-value {
        font-size: 24px;
    }

    .reports-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .reports-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .report-header {
        padding: 20px 20px 10px 20px;
    }

    .report-features {
        padding: 0 20px 10px 20px;
    }

    .report-actions {
        padding: 15px 20px 20px 20px;
        flex-direction: column;
    }

    .report-actions .btn {
        width: 100%;
        min-width: auto;
    }
}

/* تنسيقات رفع درجات الطلاب */
.bulk-upload-filters {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.bulk-upload-filters .filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 150px;
}

.bulk-upload-filters label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.bulk-upload-filters select {
    padding: 10px 12px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.bulk-upload-filters select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.bulk-upload-instructions {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
}

.instructions-card {
    padding: 25px;
}

.instructions-card h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.instructions-card ol {
    margin: 0 0 15px 0;
    padding-right: 20px;
    color: #555;
}

.instructions-card li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.instructions-note {
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.2);
    border-radius: 8px;
    padding: 12px;
    color: #2c3e50;
    font-size: 14px;
}

.bulk-upload-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow-x: auto;
}

.bulk-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    min-width: 800px;
}

.bulk-table th {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    border: 1px solid #34495e;
    position: sticky;
    top: 0;
    z-index: 10;
}

.bulk-table td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: center;
    position: relative;
}

.bulk-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.bulk-table tbody tr:hover {
    background: #e3f2fd;
}

.bulk-table .student-info {
    background: #f1f3f4;
    font-weight: 500;
    color: #2c3e50;
}

.bulk-table .editable-cell {
    background: white;
    cursor: text;
    transition: all 0.3s ease;
    min-width: 80px;
}

.bulk-table .editable-cell:hover {
    background: #fff3e0;
    box-shadow: inset 0 0 0 2px #ff9800;
}

.bulk-table .editable-cell:focus {
    background: #fff;
    box-shadow: inset 0 0 0 2px #2196f3;
    outline: none;
}

.bulk-table .editable-cell.modified {
    background: #e8f5e8;
    border-color: #4caf50;
}

.bulk-table .editable-cell.error {
    background: #ffebee;
    border-color: #f44336;
}

.bulk-table input {
    width: 100%;
    border: none;
    background: transparent;
    text-align: center;
    font-size: 14px;
    padding: 4px;
}

.bulk-table input:focus {
    outline: none;
}

.bulk-stats {
    display: flex;
    gap: 15px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.bulk-stat-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    text-align: center;
    min-width: 120px;
}

.bulk-stat-number {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    display: block;
}

.bulk-stat-label {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

.bulk-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.paste-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(33, 150, 243, 0.2);
    border: 2px dashed #2196f3;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.paste-indicator.active {
    opacity: 1;
}

/* حالة الجدول الفارغ */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state .empty-icon {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 24px;
}

.empty-state p {
    color: #7f8c8d;
    font-size: 16px;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.5;
}

/* تنسيقات الطلاب المؤقتين */
tr[data-temporary="true"] {
    background: #f8f9fa;
    border-left: 3px solid #17a2b8;
}

tr[data-temporary="true"] .student-info.editable-cell {
    background: #e3f2fd;
    border: 1px dashed #2196f3;
}

tr[data-temporary="true"] .student-info.editable-cell:hover {
    background: #bbdefb;
}

tr[data-temporary="true"] .student-info.editable-cell:focus {
    background: #fff;
    border-style: solid;
}

/* تحسينات للخلايا القابلة للتحرير */
.editable-cell[placeholder]:empty:before {
    content: attr(placeholder);
    color: #999;
    font-style: italic;
}

.editable-cell:focus {
    background: #fff !important;
    box-shadow: inset 0 0 0 2px #2196f3 !important;
    outline: none;
}

/* تمييز الصفوف النشطة */
.bulk-table tbody tr:hover {
    background: #f0f8ff !important;
}

.bulk-table tbody tr.active {
    background: #e3f2fd !important;
    border-left: 3px solid #2196f3;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .bulk-upload-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .bulk-upload-filters .filter-group {
        min-width: auto;
    }

    .bulk-table {
        font-size: 12px;
    }

    .bulk-table th,
    .bulk-table td {
        padding: 6px 4px;
    }

    .bulk-stats {
        justify-content: center;
    }

    .bulk-stat-card {
        min-width: 100px;
    }

    .bulk-actions {
        flex-direction: column;
    }

    .bulk-actions .btn {
        width: 100%;
    }
}

/* تنسيقات الإحصائيات المتقدمة */
.advanced-statistics-dashboard {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.statistics-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.statistics-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.stats-controls {
    display: flex;
    gap: 10px;
}

.stats-controls .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
}

.stats-controls .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

.statistics-summary {
    padding: 25px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    background: #f8f9fa;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.summary-card.excellent .summary-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.summary-card.good .summary-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
}

.summary-card.warning .summary-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
}

.summary-card.danger .summary-icon {
    background: linear-gradient(135deg, #fa709a, #fee140);
    color: white;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.summary-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.summary-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    line-height: 1;
}

.summary-percentage {
    font-size: 12px;
    color: #7f8c8d;
    font-weight: 500;
}

/* شبكة الرسوم البيانية */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 25px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.chart-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    margin: 0;
    font-size: 16px;
    color: #2c3e50;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 5px;
}

.chart-container {
    padding: 20px;
    height: 300px;
    position: relative;
}

/* التحليل التفصيلي */
.detailed-analysis {
    padding: 25px;
    background: #f8f9fa;
}

.analysis-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.analysis-card h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.analysis-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid var(--primary-color);
}

.analysis-item h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
}

.analysis-item p {
    margin: 0;
    color: #666;
    line-height: 1.5;
    font-size: 14px;
}

.recommendations-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recommendations-list li {
    padding: 8px 0;
    color: #666;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.recommendations-list i {
    color: var(--primary-color);
    font-size: 12px;
}

/* الرسوم البيانية التفاعلية */
.interactive-charts-dashboard {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.charts-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.charts-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.charts-controls {
    display: flex;
    gap: 10px;
}

.charts-controls .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
}

.charts-controls .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

.charts-tabs {
    background: #f8f9fa;
    padding: 0;
    display: flex;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    background: transparent;
    border: none;
    padding: 15px 25px;
    color: #666;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

.charts-content {
    min-height: 500px;
}

.charts-tab-content {
    display: none;
}

.charts-tab-content.active {
    display: block;
}

.charts-grid-large {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    padding: 25px;
}

.chart-card-large {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.chart-card-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.chart-container-large {
    padding: 25px;
    height: 400px;
    position: relative;
}

/* تحسينات الاستجابة للرسوم البيانية */
@media (max-width: 768px) {
    .statistics-summary {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        padding: 20px;
    }

    .summary-card {
        padding: 15px;
        gap: 10px;
    }

    .summary-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .summary-number {
        font-size: 20px;
    }

    .charts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 20px;
    }

    .charts-grid-large {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }

    .chart-container {
        height: 250px;
        padding: 15px;
    }

    .chart-container-large {
        height: 300px;
        padding: 20px;
    }

    .charts-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        padding: 12px 15px;
        font-size: 14px;
    }

    .analysis-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* Responsive Design for Report Actions */
@media (max-width: 768px) {
    .print-buttons-grid,
    .export-buttons-grid,
    .preview-buttons-grid {
        grid-template-columns: 1fr;
    }

    .print-info-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .report-actions {
        padding: 1rem;
    }

    .print-options-section,
    .export-options-section,
    .preview-options-section {
        padding: 1rem;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.hidden { display: none; }
.visible { display: block; }
