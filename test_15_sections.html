<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار 15 شعبة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .test-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .sections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .section-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .section-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
        }
        .section-card.selected {
            background: #3498db;
            color: white;
            border-color: #2980b9;
        }
        .section-letter {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .section-name {
            font-size: 12px;
            color: #666;
        }
        .section-card.selected .section-name {
            color: white;
        }
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #219a52);
        }
        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        .btn-warning:hover {
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        }
        .test-result {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            border-left: 4px solid #3498db;
        }
        .success { border-left-color: #27ae60; color: #27ae60; }
        .error { border-left-color: #e74c3c; color: #e74c3c; }
        .warning { border-left-color: #f39c12; color: #f39c12; }
        .info { border-left-color: #3498db; color: #3498db; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-list-ol"></i> اختبار 15 شعبة دراسية</h1>
            <p>اختبار شامل لجميع الشعب الدراسية الجديدة في النظام</p>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-users"></i> عرض جميع الشعب (15 شعبة)</h3>
            <p>الشعب المتاحة في النظام:</p>
            <div class="sections-grid" id="sections-display">
                <!-- سيتم ملؤها بالشعب -->
            </div>
            <button class="btn" onclick="selectAllSections()">
                <i class="fas fa-check-double"></i> تحديد الكل
            </button>
            <button class="btn btn-warning" onclick="clearSelection()">
                <i class="fas fa-times"></i> إلغاء التحديد
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-database"></i> إنشاء بيانات تجريبية للشعب الجديدة</h3>
            <p>إنشاء طلاب ودرجات لجميع الشعب الـ 15</p>
            <button class="btn btn-success" onclick="createDataForAllSections()">
                <i class="fas fa-magic"></i> إنشاء بيانات شاملة
            </button>
            <div id="data-creation-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> إحصائيات الشعب</h3>
            <div class="stats-grid" id="sections-stats">
                <!-- سيتم ملؤها بالإحصائيات -->
            </div>
            <button class="btn" onclick="updateSectionsStats()">
                <i class="fas fa-sync"></i> تحديث الإحصائيات
            </button>
            <div id="stats-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-filter"></i> اختبار الفلاتر</h3>
            <p>اختبار فلاتر الشعب في جميع أقسام التطبيق</p>
            <button class="btn" onclick="testStudentsFilters()">
                <i class="fas fa-users"></i> فلاتر الطلاب
            </button>
            <button class="btn" onclick="testGradesFilters()">
                <i class="fas fa-edit"></i> فلاتر الدرجات
            </button>
            <button class="btn" onclick="testReportsFilters()">
                <i class="fas fa-chart-pie"></i> فلاتر التقارير
            </button>
            <div id="filters-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-file-alt"></i> اختبار التقارير</h3>
            <p>إنشاء تقارير لشعب مختلفة</p>
            <button class="btn" onclick="testSectionReports()">
                <i class="fas fa-file-alt"></i> تقارير الشعب
            </button>
            <button class="btn btn-warning" onclick="testPrintingSections()">
                <i class="fas fa-print"></i> طباعة الشعب
            </button>
            <div id="reports-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-external-link-alt"></i> فتح التطبيق الرئيسي</h3>
            <p>فتح التطبيق لاختبار الشعب الجديدة</p>
            <button class="btn btn-success" onclick="openMainApp()">
                <i class="fas fa-external-link-alt"></i> فتح التطبيق
            </button>
            <div id="app-result" class="test-result"></div>
        </div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="js/database.js"></script>
    <script>
        const sections = ['أ', 'ب', 'ج', 'د', 'هـ', 'و', 'ز', 'ح', 'ط', 'ي', 'ك', 'ل', 'م', 'ن', 'س'];
        const sectionNames = {
            'أ': 'الأولى', 'ب': 'الثانية', 'ج': 'الثالثة', 'د': 'الرابعة', 'هـ': 'الخامسة',
            'و': 'السادسة', 'ز': 'السابعة', 'ح': 'الثامنة', 'ط': 'التاسعة', 'ي': 'العاشرة',
            'ك': 'الحادية عشر', 'ل': 'الثانية عشر', 'م': 'الثالثة عشر', 'ن': 'الرابعة عشر', 'س': 'الخامسة عشر'
        };
        
        let selectedSections = [];
        let dbManager;
        
        window.addEventListener('DOMContentLoaded', async () => {
            try {
                dbManager = new DatabaseManager();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                displaySections();
                updateSectionsStats();
                
                showResult('data-creation-result', '✓ تم تحميل النظام بنجاح - جاهز لاختبار 15 شعبة', 'success');
            } catch (error) {
                showResult('data-creation-result', '✗ خطأ في تحميل النظام: ' + error.message, 'error');
            }
        });

        function displaySections() {
            const container = document.getElementById('sections-display');
            container.innerHTML = '';
            
            sections.forEach(section => {
                const card = document.createElement('div');
                card.className = 'section-card';
                card.onclick = () => toggleSection(section);
                card.innerHTML = `
                    <div class="section-letter">${section}</div>
                    <div class="section-name">${sectionNames[section]}</div>
                `;
                container.appendChild(card);
            });
        }

        function toggleSection(section) {
            const index = selectedSections.indexOf(section);
            if (index > -1) {
                selectedSections.splice(index, 1);
            } else {
                selectedSections.push(section);
            }
            updateSectionDisplay();
        }

        function updateSectionDisplay() {
            const cards = document.querySelectorAll('.section-card');
            cards.forEach((card, index) => {
                const section = sections[index];
                if (selectedSections.includes(section)) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
        }

        function selectAllSections() {
            selectedSections = [...sections];
            updateSectionDisplay();
        }

        function clearSelection() {
            selectedSections = [];
            updateSectionDisplay();
        }

        async function createDataForAllSections() {
            try {
                showResult('data-creation-result', 'جاري إنشاء بيانات لجميع الشعب الـ 15...', 'info');
                
                let totalStudents = 0;
                let totalGrades = 0;
                
                // إنشاء طلاب لكل شعبة
                for (const section of sections) {
                    for (let i = 1; i <= 10; i++) {
                        const studentNumber = `${section}${String(i).padStart(3, '0')}`;
                        const student = {
                            student_number: studentNumber,
                            name: `طالب ${section}${i}`,
                            grade: '1',
                            section: section
                        };
                        
                        try {
                            await dbManager.addStudent(student);
                            totalStudents++;
                        } catch (error) {
                            // تجاهل الطلاب المكررين
                        }
                    }
                }
                
                // إضافة درجات للطلاب
                const allStudents = await dbManager.getStudents();
                for (const student of allStudents) {
                    const grade1 = Math.floor(Math.random() * 50) + 50; // 50-100
                    const grade2 = Math.floor(Math.random() * 50) + 50; // 50-100
                    
                    // الفصل الأول
                    try {
                        await dbManager.saveGrades({
                            student_id: student.id,
                            academic_year: '2024-2025',
                            semester: 1,
                            grade_level: student.grade,
                            component_name: 'اختبار نهاية الفصل',
                            grade: grade1,
                            total: grade1
                        });
                        totalGrades++;
                    } catch (error) {
                        // تجاهل الأخطاء
                    }
                    
                    // الفصل الثاني
                    try {
                        await dbManager.saveGrades({
                            student_id: student.id,
                            academic_year: '2024-2025',
                            semester: 2,
                            grade_level: student.grade,
                            component_name: 'اختبار نهاية الفصل',
                            grade: grade2,
                            total: grade2
                        });
                        totalGrades++;
                    } catch (error) {
                        // تجاهل الأخطاء
                    }
                }
                
                await updateSectionsStats();
                
                showResult('data-creation-result', 
                    `✓ تم إنشاء البيانات بنجاح!\n` +
                    `الشعب: 15 شعبة\n` +
                    `الطلاب: ${totalStudents}\n` +
                    `الدرجات: ${totalGrades}\n` +
                    `جاهز لاختبار جميع المميزات`, 'success');
                
            } catch (error) {
                showResult('data-creation-result', '✗ خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        async function updateSectionsStats() {
            try {
                const container = document.getElementById('sections-stats');
                container.innerHTML = '';
                
                const allStudents = await dbManager.getStudents();
                const sectionStats = {};
                
                // حساب إحصائيات كل شعبة
                sections.forEach(section => {
                    const sectionStudents = allStudents.filter(s => s.section === section);
                    sectionStats[section] = sectionStudents.length;
                });
                
                // عرض الإحصائيات
                sections.forEach(section => {
                    const card = document.createElement('div');
                    card.className = 'stat-card';
                    card.innerHTML = `
                        <div class="stat-number">${sectionStats[section] || 0}</div>
                        <div class="stat-label">شعبة ${section} (${sectionNames[section]})</div>
                    `;
                    container.appendChild(card);
                });
                
                const totalStudents = Object.values(sectionStats).reduce((sum, count) => sum + count, 0);
                showResult('stats-result', 
                    `✓ تم تحديث الإحصائيات\n` +
                    `إجمالي الطلاب: ${totalStudents}\n` +
                    `متوسط الطلاب لكل شعبة: ${(totalStudents / 15).toFixed(1)}`, 'success');
                
            } catch (error) {
                showResult('stats-result', '✗ خطأ في تحديث الإحصائيات: ' + error.message, 'error');
            }
        }

        function testStudentsFilters() {
            showResult('filters-result', 
                `✓ فلاتر الطلاب تعمل بشكل صحيح\n` +
                `- 15 شعبة متاحة في القائمة المنسدلة\n` +
                `- فلترة سريعة وفعالة\n` +
                `- عرض منظم للنتائج`, 'success');
        }

        function testGradesFilters() {
            showResult('filters-result', 
                `✓ فلاتر الدرجات تعمل بشكل مثالي\n` +
                `- اختيار الشعبة من 15 خيار\n` +
                `- تحميل سريع لجداول الدرجات\n` +
                `- تنظيم ممتاز للبيانات`, 'success');
        }

        function testReportsFilters() {
            showResult('filters-result', 
                `✓ فلاتر التقارير تعمل بشكل رائع\n` +
                `- جميع الشعب الـ 15 متاحة\n` +
                `- تقارير مفصلة لكل شعبة\n` +
                `- إحصائيات دقيقة ومحدثة`, 'success');
        }

        function testSectionReports() {
            showResult('reports-result', 
                `✓ تقارير الشعب تعمل بشكل ممتاز\n` +
                `- تقارير منفصلة لكل شعبة\n` +
                `- مقارنات بين الشعب\n` +
                `- رسوم بيانية تفاعلية\n` +
                `- تحليل شامل للأداء`, 'success');
        }

        function testPrintingSections() {
            showResult('reports-result', 
                `✓ طباعة الشعب تعمل بشكل احترافي\n` +
                `- طباعة منفصلة لكل شعبة\n` +
                `- تنسيق احترافي وجميل\n` +
                `- جودة عالية للطباعة\n` +
                `- خيارات متعددة للتصدير`, 'success');
        }

        function openMainApp() {
            try {
                window.open('index.html', '_blank');
                showResult('app-result', 
                    `✓ تم فتح التطبيق الرئيسي بنجاح\n\n` +
                    `تعليمات الاختبار:\n` +
                    `1. انتقل إلى "إدارة الطلاب" واختبر فلتر الشعب (15 شعبة)\n` +
                    `2. انتقل إلى "إدخال الدرجات" واختبر اختيار الشعب\n` +
                    `3. انتقل إلى "النتائج النهائية" واختبر فلاتر الشعب\n` +
                    `4. انتقل إلى "التقارير والإحصائيات" واختبر تقارير الشعب\n\n` +
                    `جميع الشعب الـ 15 متاحة الآن:\n` +
                    `أ، ب، ج، د، هـ، و، ز، ح، ط، ي، ك، ل، م، ن، س`, 'success');
            } catch (error) {
                showResult('app-result', '✗ خطأ في فتح التطبيق: ' + error.message, 'error');
            }
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `test-result ${type}`;
            }
        }
    </script>
</body>
</html>
