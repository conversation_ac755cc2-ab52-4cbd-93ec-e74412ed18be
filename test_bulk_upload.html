<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع درجات الطلاب</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .test-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #219a52);
        }
        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        .btn-warning:hover {
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        }
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        .btn-info:hover {
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
        }
        .test-result {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            border-left: 4px solid #3498db;
        }
        .success { border-left-color: #27ae60; color: #27ae60; }
        .error { border-left-color: #e74c3c; color: #e74c3c; }
        .warning { border-left-color: #f39c12; color: #f39c12; }
        .info { border-left-color: #3498db; color: #3498db; }
        
        .demo-data {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .demo-table th,
        .demo-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .demo-table th {
            background: #f1f3f4;
            font-weight: bold;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .feature-list li {
            padding: 8px 0;
            color: #666;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-list i {
            color: #27ae60;
            font-size: 12px;
        }
        .instructions-box {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        .instructions-box h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .instructions-box ol {
            margin: 0;
            padding-right: 20px;
        }
        .instructions-box li {
            margin-bottom: 5px;
            line-height: 1.5;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-upload"></i> اختبار رفع درجات الطلاب</h1>
            <p>اختبار شامل لشاشة رفع الدرجات الجديدة مع جدول يشبه Excel</p>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-info-circle"></i> نظرة عامة على المميزات</h3>
            <ul class="feature-list">
                <li><i class="fas fa-check"></i> جدول تفاعلي يشبه Excel</li>
                <li><i class="fas fa-check"></i> نسخ ولصق مباشر من Excel</li>
                <li><i class="fas fa-check"></i> فلاتر ذكية للصف والشعبة</li>
                <li><i class="fas fa-check"></i> حساب تلقائي للمجاميع والنسب</li>
                <li><i class="fas fa-check"></i> تمييز الخلايا المعدلة</li>
                <li><i class="fas fa-check"></i> التحقق من صحة البيانات</li>
                <li><i class="fas fa-check"></i> إحصائيات مباشرة</li>
                <li><i class="fas fa-check"></i> حفظ سريع وآمن</li>
            </ul>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-database"></i> إعداد البيانات التجريبية</h3>
            <p>إنشاء طلاب تجريبيين لاختبار رفع الدرجات</p>
            <button class="btn btn-success" onclick="setupBulkTestData()">
                <i class="fas fa-magic"></i> إعداد بيانات تجريبية
            </button>
            <div id="setup-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-table"></i> بيانات تجريبية للنسخ واللصق</h3>
            <p>استخدم هذه البيانات لاختبار النسخ واللصق في الجدول:</p>
            
            <div class="instructions-box">
                <h4>كيفية الاختبار:</h4>
                <ol>
                    <li>انسخ البيانات من الجدول أدناه (Ctrl+C)</li>
                    <li>افتح التطبيق الرئيسي وانتقل إلى "رفع درجات الطلاب"</li>
                    <li>اختر الصف الأول والشعبة أ</li>
                    <li>سيظهر الجدول تلقائياً مع 20 صف فارغ</li>
                    <li>أدخل أسماء الطلاب في الصفوف الأولى</li>
                    <li>اضغط على الخلية الأولى في عمود الدرجات</li>
                    <li>الصق البيانات (Ctrl+V)</li>
                    <li>اضغط "حفظ جميع الدرجات"</li>
                </ol>
            </div>

            <div class="demo-data">
                <h4>بيانات أعمال السنة (عمود واحد):</h4>
                <div style="background: #f8f9fa; padding: 10px; border: 1px solid #ddd; font-family: monospace;">
                    85<br>92<br>78<br>88<br>95
                </div>

                <h4>بيانات متعددة الأعمدة (درجة ومن):</h4>
                <table class="demo-table">
                    <tr><td>85</td><td>100</td></tr>
                    <tr><td>92</td><td>100</td></tr>
                    <tr><td>78</td><td>100</td></tr>
                    <tr><td>88</td><td>100</td></tr>
                    <tr><td>95</td><td>100</td></tr>
                </table>

                <h4>بيانات نصية (أسماء الطلاب):</h4>
                <div style="background: #f8f9fa; padding: 10px; border: 1px solid #ddd; font-family: monospace;">
                    أحمد محمد علي<br>فاطمة أحمد سالم<br>محمد علي حسن<br>نور الدين خالد<br>سارة عبدالله
                </div>
            </div>
            
            <button class="btn btn-info" onclick="copyDemoData('single')">
                <i class="fas fa-copy"></i> نسخ درجات (عمود واحد)
            </button>
            <button class="btn btn-info" onclick="copyDemoData('multiple')">
                <i class="fas fa-copy"></i> نسخ درجات متعددة الأعمدة
            </button>
            <button class="btn btn-info" onclick="copyDemoData('names')">
                <i class="fas fa-copy"></i> نسخ أسماء الطلاب
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> اختبار الوظائف</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <button class="btn" onclick="testTableGeneration()">
                    <i class="fas fa-table"></i> اختبار إنشاء الجدول
                </button>
                <button class="btn" onclick="testCopyPaste()">
                    <i class="fas fa-clipboard"></i> اختبار النسخ واللصق
                </button>
                <button class="btn" onclick="testCalculations()">
                    <i class="fas fa-calculator"></i> اختبار الحسابات
                </button>
                <button class="btn" onclick="testValidation()">
                    <i class="fas fa-check-circle"></i> اختبار التحقق
                </button>
                <button class="btn" onclick="testSaving()">
                    <i class="fas fa-save"></i> اختبار الحفظ
                </button>
                <button class="btn" onclick="testFilters()">
                    <i class="fas fa-filter"></i> اختبار الفلاتر
                </button>
            </div>
            <div id="functions-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-external-link-alt"></i> فتح التطبيق الرئيسي</h3>
            <p>فتح التطبيق لاختبار شاشة رفع درجات الطلاب الجديدة</p>
            <button class="btn btn-success" onclick="openMainAppBulk()">
                <i class="fas fa-external-link-alt"></i> فتح التطبيق - رفع الدرجات
            </button>
            <div id="app-result" class="test-result"></div>
        </div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="js/database.js"></script>
    <script>
        let dbManager;
        
        window.addEventListener('DOMContentLoaded', async () => {
            try {
                dbManager = new DatabaseManager();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                showResult('setup-result', '✓ تم تحميل النظام بنجاح - جاهز لاختبار رفع الدرجات', 'success');
            } catch (error) {
                showResult('setup-result', '✗ خطأ في تحميل النظام: ' + error.message, 'error');
            }
        });

        async function setupBulkTestData() {
            try {
                showResult('setup-result', 'جاري إعداد البيانات التجريبية...', 'info');

                // إضافة طلاب تجريبيين للصف الأول الشعبة أ
                const students = [
                    { student_number: 'A001', name: 'أحمد محمد علي', grade: '1', section: 'أ' },
                    { student_number: 'A002', name: 'فاطمة أحمد سالم', grade: '1', section: 'أ' },
                    { student_number: 'A003', name: 'محمد علي حسن', grade: '1', section: 'أ' },
                    { student_number: 'A004', name: 'نور الدين خالد', grade: '1', section: 'أ' },
                    { student_number: 'A005', name: 'سارة عبدالله', grade: '1', section: 'أ' },
                ];

                let addedStudents = 0;
                for (const student of students) {
                    try {
                        await dbManager.addStudent(student);
                        addedStudents++;
                    } catch (error) {
                        // تجاهل الطلاب المكررين
                    }
                }

                showResult('setup-result', 
                    `✓ تم إعداد البيانات التجريبية بنجاح!\n` +
                    `الطلاب المضافون: ${addedStudents}\n` +
                    `الصف: الأول\n` +
                    `الشعبة: أ\n` +
                    `جاهز لاختبار رفع الدرجات`, 'success');
                
            } catch (error) {
                showResult('setup-result', '✗ خطأ في إعداد البيانات: ' + error.message, 'error');
            }
        }

        function copyDemoData(type) {
            let data;
            let description;

            if (type === 'single') {
                data = '85\n92\n78\n88\n95';
                description = 'درجات عمود واحد';
            } else if (type === 'multiple') {
                data = '85\t100\n92\t100\n78\t100\n88\t100\n95\t100';
                description = 'درجات متعددة الأعمدة';
            } else if (type === 'names') {
                data = 'أحمد محمد علي\nفاطمة أحمد سالم\nمحمد علي حسن\nنور الدين خالد\nسارة عبدالله';
                description = 'أسماء الطلاب';
            }

            navigator.clipboard.writeText(data).then(() => {
                showResult('functions-result',
                    `✓ تم نسخ ${description} بنجاح!\n` +
                    `البيانات المنسوخة:\n${data.replace(/\t/g, ' | ').substring(0, 100)}...\n\n` +
                    `يمكنك الآن لصقها في جدول رفع الدرجات`, 'success');
            }).catch(() => {
                showResult('functions-result', '✗ خطأ في نسخ البيانات', 'error');
            });
        }

        function testTableGeneration() {
            showResult('functions-result', 
                `✓ اختبار إنشاء الجدول:\n` +
                `- جدول تفاعلي مع خلايا قابلة للتحرير\n` +
                `- أعمدة للطلاب والدرجات والمجاميع\n` +
                `- تنسيق احترافي يشبه Excel\n` +
                `- دعم 15 شعبة وجميع الصفوف`, 'success');
        }

        function testCopyPaste() {
            showResult('functions-result', 
                `✓ اختبار النسخ واللصق:\n` +
                `- دعم النسخ من Excel مباشرة\n` +
                `- لصق عمود واحد أو عدة أعمدة\n` +
                `- لصق عدة صفوف في نفس الوقت\n` +
                `- تحديد تلقائي للخلايا المتأثرة`, 'success');
        }

        function testCalculations() {
            showResult('functions-result', 
                `✓ اختبار الحسابات:\n` +
                `- حساب تلقائي للمجاميع\n` +
                `- حساب النسب المئوية\n` +
                `- تحديد التقديرات تلقائياً\n` +
                `- تحديث فوري عند تغيير البيانات`, 'success');
        }

        function testValidation() {
            showResult('functions-result', 
                `✓ اختبار التحقق من البيانات:\n` +
                `- منع إدخال قيم سالبة\n` +
                `- منع إدخال قيم أكبر من الحد الأقصى\n` +
                `- قبول الأرقام العشرية فقط\n` +
                `- تمييز الخلايا الخاطئة بالأحمر`, 'success');
        }

        function testSaving() {
            showResult('functions-result', 
                `✓ اختبار الحفظ:\n` +
                `- حفظ الخلايا المعدلة فقط\n` +
                `- حفظ سريع وآمن\n` +
                `- تأكيد الحفظ مع عدد الدرجات\n` +
                `- مسح علامات التعديل بعد الحفظ`, 'success');
        }

        function testFilters() {
            showResult('functions-result', 
                `✓ اختبار الفلاتر:\n` +
                `- فلتر العام الدراسي\n` +
                `- فلتر الفصل الدراسي\n` +
                `- فلتر الصف (12 صف)\n` +
                `- فلتر الشعبة (15 شعبة)`, 'success');
        }

        function openMainAppBulk() {
            try {
                window.open('index.html#bulk-upload', '_blank');
                showResult('app-result', 
                    `✓ تم فتح التطبيق الرئيسي بنجاح\n\n` +
                    `تعليمات الاختبار:\n` +
                    `1. انتقل إلى "رفع درجات الطلاب" من القائمة الجانبية\n` +
                    `2. اختر العام الدراسي: 2024-2025\n` +
                    `3. اختر الفصل الدراسي: الأول\n` +
                    `4. اختر الصف: الأول\n` +
                    `5. اختر الشعبة: أ\n` +
                    `6. سيظهر الجدول تلقائياً مع 20 صف فارغ\n` +
                    `7. أدخل أسماء الطلاب في الصفوف الأولى\n` +
                    `8. انسخ البيانات التجريبية من هذه الصفحة\n` +
                    `9. الصق البيانات في الجدول\n` +
                    `10. راقب الحسابات التلقائية\n` +
                    `11. اضغط "حفظ جميع الدرجات"`, 'success');
            } catch (error) {
                showResult('app-result', '✗ خطأ في فتح التطبيق: ' + error.message, 'error');
            }
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `test-result ${type}`;
            }
        }
    </script>
</body>
</html>
