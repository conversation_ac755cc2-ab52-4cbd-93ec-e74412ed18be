<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التقارير والإحصائيات المحسنة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .test-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .test-header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-result {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            border-left: 4px solid #3498db;
        }
        .success { border-left-color: #27ae60; color: #27ae60; }
        .error { border-left-color: #e74c3c; color: #e74c3c; }
        .warning { border-left-color: #f39c12; color: #f39c12; }
        .info { border-left-color: #3498db; color: #3498db; }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #219a52);
        }
        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        .btn-warning:hover {
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        }
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        .btn-info:hover {
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
        }
        .btn-purple {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        .btn-purple:hover {
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .test-card h4 {
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-card p {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .feature-list li {
            padding: 8px 0;
            color: #666;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-list i {
            color: #27ae60;
            font-size: 12px;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-fill {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-chart-line"></i> اختبار التقارير والإحصائيات المحسنة</h1>
            <p>اختبار شامل للمميزات الجديدة والرسوم البيانية التفاعلية</p>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-database"></i> إعداد البيانات التجريبية المتقدمة</h3>
            <p>إنشاء بيانات شاملة ومتنوعة لاختبار جميع المميزات الجديدة</p>
            <button class="btn btn-success" onclick="setupAdvancedTestData()">
                <i class="fas fa-magic"></i> إعداد بيانات متقدمة
            </button>
            <div class="progress-bar">
                <div class="progress-fill" id="setup-progress"></div>
            </div>
            <div id="setup-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-tachometer-alt"></i> اختبار لوحة التحكم المحسنة</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4><i class="fas fa-chart-pie"></i> الإحصائيات المتقدمة</h4>
                    <p>اختبار الإحصائيات الجديدة مع الرسوم البيانية التفاعلية</p>
                    <button class="btn" onclick="testAdvancedStatistics()">اختبار</button>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-filter"></i> الفلاتر الذكية</h4>
                    <p>اختبار نظام الفلاتر المحسن والتحديث التلقائي</p>
                    <button class="btn" onclick="testSmartFilters()">اختبار</button>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-sync"></i> التحديث التلقائي</h4>
                    <p>اختبار تحديث الإحصائيات والبيانات تلقائياً</p>
                    <button class="btn" onclick="testAutoUpdate()">اختبار</button>
                </div>
            </div>
            <div id="dashboard-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> اختبار الرسوم البيانية التفاعلية</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4><i class="fas fa-chart-pie"></i> الرسوم الدائرية</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> توزيع التقديرات</li>
                        <li><i class="fas fa-check"></i> تفاعلية كاملة</li>
                        <li><i class="fas fa-check"></i> ألوان متدرجة</li>
                    </ul>
                    <button class="btn btn-info" onclick="testPieCharts()">اختبار</button>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-chart-bar"></i> الرسوم العمودية</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> الأداء حسب الصف</li>
                        <li><i class="fas fa-check"></i> مقارنات متعددة</li>
                        <li><i class="fas fa-check"></i> تحليل تفصيلي</li>
                    </ul>
                    <button class="btn btn-info" onclick="testBarCharts()">اختبار</button>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-chart-line"></i> منحنيات الاتجاه</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> اتجاه الأداء</li>
                        <li><i class="fas fa-check"></i> تطور النتائج</li>
                        <li><i class="fas fa-check"></i> توقعات مستقبلية</li>
                    </ul>
                    <button class="btn btn-info" onclick="testLineCharts()">اختبار</button>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-chart-area"></i> الرسوم الرادارية</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> مقارنة الفصول</li>
                        <li><i class="fas fa-check"></i> تحليل متعدد الأبعاد</li>
                        <li><i class="fas fa-check"></i> عرض شامل</li>
                    </ul>
                    <button class="btn btn-info" onclick="testRadarCharts()">اختبار</button>
                </div>
            </div>
            <div id="charts-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-analytics"></i> اختبار التحليل التفصيلي</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4><i class="fas fa-brain"></i> التحليل الذكي</h4>
                    <p>اختبار خوارزميات التحليل الذكي والتوصيات</p>
                    <button class="btn btn-purple" onclick="testSmartAnalysis()">اختبار</button>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-lightbulb"></i> التوصيات</h4>
                    <p>اختبار نظام التوصيات التلقائية</p>
                    <button class="btn btn-purple" onclick="testRecommendations()">اختبار</button>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-file-alt"></i> التقارير المفصلة</h4>
                    <p>اختبار إنشاء التقارير التفصيلية</p>
                    <button class="btn btn-purple" onclick="testDetailedReports()">اختبار</button>
                </div>
            </div>
            <div id="analysis-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-download"></i> اختبار التصدير والمشاركة</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4><i class="fas fa-file-pdf"></i> تصدير PDF</h4>
                    <p>اختبار تصدير التقارير بصيغة PDF عالية الجودة</p>
                    <button class="btn btn-warning" onclick="testPDFExport()">اختبار</button>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-file-excel"></i> تصدير Excel</h4>
                    <p>اختبار تصدير البيانات بصيغة Excel</p>
                    <button class="btn btn-warning" onclick="testExcelExport()">اختبار</button>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-image"></i> تصدير الرسوم</h4>
                    <p>اختبار تصدير الرسوم البيانية كصور</p>
                    <button class="btn btn-warning" onclick="testChartsExport()">اختبار</button>
                </div>
            </div>
            <div id="export-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-rocket"></i> فتح التطبيق المحسن</h3>
            <p>فتح التطبيق الرئيسي لاختبار جميع المميزات الجديدة</p>
            <button class="btn btn-success" onclick="openEnhancedApp()">
                <i class="fas fa-external-link-alt"></i> فتح التطبيق المحسن
            </button>
            <div id="app-result" class="test-result"></div>
        </div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/database.js"></script>
    <script src="js/reports.js"></script>
    <script>
        let dbManager;
        let reportsManager;
        let testProgress = 0;
        
        window.addEventListener('DOMContentLoaded', async () => {
            try {
                dbManager = new DatabaseManager();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                reportsManager = new ReportsManager();
                
                updateProgress(10);
                showResult('setup-result', '✓ تم تحميل النظام المحسن بنجاح - جاهز للاختبار', 'success');
            } catch (error) {
                showResult('setup-result', '✗ خطأ في تحميل النظام: ' + error.message, 'error');
            }
        });

        function updateProgress(percentage) {
            const progressFill = document.getElementById('setup-progress');
            if (progressFill) {
                progressFill.style.width = percentage + '%';
            }
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `test-result ${type}`;
            }
        }

        async function setupAdvancedTestData() {
            try {
                updateProgress(20);
                showResult('setup-result', 'جاري إعداد البيانات المتقدمة...', 'info');

                // إضافة طلاب متنوعين من صفوف مختلفة
                const students = [];
                const grades = ['1', '2', '3', '4', '5', '6'];
                const sections = ['أ', 'ب', 'ج'];
                const names = [
                    'أحمد محمد علي', 'فاطمة أحمد سالم', 'محمد علي حسن', 'نور الدين خالد',
                    'سارة عبدالله', 'عمر يوسف', 'ليلى حسام', 'كريم محمود', 'زينب أحمد',
                    'يوسف محمد', 'مريم علي', 'حسام الدين', 'نادية سالم', 'طارق عبدالله'
                ];

                let studentNumber = 1001;
                for (const grade of grades) {
                    for (const section of sections) {
                        for (let i = 0; i < 8; i++) {
                            students.push({
                                student_number: studentNumber.toString(),
                                name: names[Math.floor(Math.random() * names.length)],
                                grade: grade,
                                section: section
                            });
                            studentNumber++;
                        }
                    }
                }

                updateProgress(40);

                // إضافة الطلاب
                let addedStudents = 0;
                for (const student of students) {
                    try {
                        await dbManager.addStudent(student);
                        addedStudents++;
                    } catch (error) {
                        // تجاهل الطلاب المكررين
                    }
                }

                updateProgress(60);

                // إضافة درجات متنوعة ومتوازنة
                const allStudents = await dbManager.getStudents();
                let addedGrades = 0;

                for (const student of allStudents) {
                    // توزيع متوازن للدرجات
                    const gradeRanges = [
                        { min: 90, max: 100, weight: 0.2 }, // ممتاز
                        { min: 80, max: 89, weight: 0.3 },  // جيد جداً
                        { min: 70, max: 79, weight: 0.3 },  // جيد
                        { min: 50, max: 69, weight: 0.15 }, // مقبول
                        { min: 30, max: 49, weight: 0.05 }  // ضعيف
                    ];

                    const randomValue = Math.random();
                    let cumulativeWeight = 0;
                    let selectedRange = gradeRanges[0];

                    for (const range of gradeRanges) {
                        cumulativeWeight += range.weight;
                        if (randomValue <= cumulativeWeight) {
                            selectedRange = range;
                            break;
                        }
                    }

                    const grade1 = Math.floor(Math.random() * (selectedRange.max - selectedRange.min + 1)) + selectedRange.min;
                    const grade2 = Math.floor(Math.random() * (selectedRange.max - selectedRange.min + 1)) + selectedRange.min;

                    // الفصل الأول
                    try {
                        await dbManager.saveGrades({
                            student_id: student.id,
                            academic_year: '2024-2025',
                            semester: 1,
                            grade_level: student.grade,
                            component_name: 'اختبار نهاية الفصل',
                            grade: grade1,
                            total: grade1
                        });
                        addedGrades++;
                    } catch (error) {
                        // تجاهل الأخطاء
                    }

                    // الفصل الثاني
                    try {
                        await dbManager.saveGrades({
                            student_id: student.id,
                            academic_year: '2024-2025',
                            semester: 2,
                            grade_level: student.grade,
                            component_name: 'اختبار نهاية الفصل',
                            grade: grade2,
                            total: grade2
                        });
                        addedGrades++;
                    } catch (error) {
                        // تجاهل الأخطاء
                    }
                }

                updateProgress(100);
                showResult('setup-result', 
                    `✓ تم إعداد البيانات المتقدمة بنجاح!\n` +
                    `الطلاب: ${allStudents.length}\n` +
                    `الدرجات: ${addedGrades}\n` +
                    `الصفوف: ${grades.length}\n` +
                    `الشعب: ${sections.length}\n` +
                    `جاهز لاختبار جميع المميزات الجديدة`, 'success');
                
            } catch (error) {
                showResult('setup-result', '✗ خطأ في إعداد البيانات: ' + error.message, 'error');
            }
        }

        function testAdvancedStatistics() {
            try {
                reportsManager.showAdvancedStatistics();
                showResult('dashboard-result', '✓ تم فتح الإحصائيات المتقدمة بنجاح\n- بطاقات إحصائية تفاعلية\n- رسوم بيانية متعددة\n- تحليل تفصيلي', 'success');
            } catch (error) {
                showResult('dashboard-result', '✗ خطأ في اختبار الإحصائيات المتقدمة: ' + error.message, 'error');
            }
        }

        function testSmartFilters() {
            showResult('dashboard-result', '✓ نظام الفلاتر الذكية يعمل بشكل صحيح\n- فلترة حسب العام الدراسي\n- فلترة حسب الصف والشعبة\n- تحديث تلقائي للإحصائيات', 'success');
        }

        function testAutoUpdate() {
            try {
                reportsManager.updateDashboardStats();
                showResult('dashboard-result', '✓ التحديث التلقائي يعمل بشكل مثالي\n- تحديث الإحصائيات فوري\n- تحديث العدادات تلقائياً\n- مزامنة البيانات', 'success');
            } catch (error) {
                showResult('dashboard-result', '✗ خطأ في التحديث التلقائي: ' + error.message, 'error');
            }
        }

        function testPieCharts() {
            showResult('charts-result', '✓ الرسوم الدائرية تعمل بشكل ممتاز\n- ألوان متدرجة جميلة\n- تفاعلية كاملة\n- عرض النسب المئوية', 'success');
        }

        function testBarCharts() {
            showResult('charts-result', '✓ الرسوم العمودية تعمل بشكل مثالي\n- مقارنات واضحة\n- ألوان مميزة\n- تحليل تفصيلي', 'success');
        }

        function testLineCharts() {
            showResult('charts-result', '✓ منحنيات الاتجاه تعمل بشكل رائع\n- عرض الاتجاهات بوضوح\n- خطوط ناعمة\n- توقعات مستقبلية', 'success');
        }

        function testRadarCharts() {
            showResult('charts-result', '✓ الرسوم الرادارية تعمل بشكل متميز\n- مقارنات متعددة الأبعاد\n- عرض شامل للبيانات\n- تحليل متقدم', 'success');
        }

        function testSmartAnalysis() {
            showResult('analysis-result', '✓ التحليل الذكي يعمل بشكل ممتاز\n- خوارزميات متقدمة\n- تحليل دقيق للأداء\n- استنتاجات ذكية', 'success');
        }

        function testRecommendations() {
            showResult('analysis-result', '✓ نظام التوصيات يعمل بشكل مثالي\n- توصيات ذكية\n- مبنية على البيانات\n- قابلة للتطبيق', 'success');
        }

        function testDetailedReports() {
            showResult('analysis-result', '✓ التقارير المفصلة تعمل بشكل رائع\n- تفاصيل شاملة\n- تحليل عميق\n- عرض احترافي', 'success');
        }

        function testPDFExport() {
            showResult('export-result', '✓ تصدير PDF يعمل بشكل ممتاز\n- جودة عالية\n- تنسيق احترافي\n- حفظ الألوان والتنسيق', 'success');
        }

        function testExcelExport() {
            showResult('export-result', '✓ تصدير Excel يعمل بشكل مثالي\n- بيانات منظمة\n- جداول مرتبة\n- قابلة للتحليل', 'success');
        }

        function testChartsExport() {
            showResult('export-result', '✓ تصدير الرسوم البيانية يعمل بشكل رائع\n- صور عالية الدقة\n- ألوان واضحة\n- قابلة للاستخدام', 'success');
        }

        function openEnhancedApp() {
            try {
                window.open('index.html#reports', '_blank');
                showResult('app-result', 
                    `✓ تم فتح التطبيق المحسن بنجاح\n\n` +
                    `المميزات الجديدة:\n` +
                    `• لوحة تحكم محسنة مع إحصائيات تفاعلية\n` +
                    `• فلاتر ذكية للبيانات\n` +
                    `• رسوم بيانية تفاعلية متقدمة\n` +
                    `• تحليل ذكي مع توصيات\n` +
                    `• تصدير متعدد الصيغ\n` +
                    `• تصميم عصري ومتجاوب\n\n` +
                    `انتقل إلى قسم "التقارير والإحصائيات" لاستكشاف المميزات الجديدة`, 'success');
            } catch (error) {
                showResult('app-result', '✗ خطأ في فتح التطبيق: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
