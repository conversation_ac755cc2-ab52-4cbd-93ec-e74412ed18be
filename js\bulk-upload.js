class BulkUploadManager {
    constructor() {
        this.currentStudents = [];
        this.modifiedCells = new Set();
        this.gradeComponents = [
            { name: 'أعمال السنة', weight: 40 },
            { name: 'اختبار منتصف الفصل', weight: 20 },
            { name: 'اختبار نهاية الفصل', weight: 40 }
        ];
        this.setupEventListeners();
    }

    setupEventListeners() {
        // إعداد مستمعي الأحداث للنسخ واللصق
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'v') {
                this.handlePaste(e);
            }
            if (e.ctrlKey && e.key === 'c') {
                this.showPastePreview();
            }
        });

        // إعداد مستمع للنقر على الخلايا
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                this.selectCell(e.target);
            }
        });

        // إعداد مستمعي تغيير الفلاتر
        document.addEventListener('change', (e) => {
            if (e.target.id === 'bulk-grade-select' || e.target.id === 'bulk-section-select') {
                this.onFiltersChange();
            }
        });
    }

    onFiltersChange() {
        const grade = document.getElementById('bulk-grade-select').value;
        const section = document.getElementById('bulk-section-select').value;

        if (grade && section) {
            // إظهار الجدول تلقائياً عند اختيار الصف والشعبة
            this.loadStudentsTable();
        } else {
            // مسح الجدول إذا لم يتم اختيار الفلاتر
            this.clearTableDisplay();
        }
    }

    async loadStudentsTable() {
        const grade = document.getElementById('bulk-grade-select').value;
        const section = document.getElementById('bulk-section-select').value;

        if (!grade || !section) {
            this.clearTableDisplay();
            return;
        }

        try {
            // تحميل الطلاب
            const students = await dbManager.getStudents({ grade, section });

            if (students.length === 0) {
                // إنشاء طلاب افتراضيين إذا لم يوجدوا
                this.currentStudents = this.generateDefaultStudents(grade, section);
                showNotification(`لا يوجد طلاب مسجلين. تم إنشاء جدول فارغ للصف ${grade} الشعبة ${section}`, 'info');
            } else {
                this.currentStudents = students;
                showNotification(`تم تحميل ${students.length} طالب`, 'success');
            }

            this.generateTable();

        } catch (error) {
            console.error('خطأ في تحميل الطلاب:', error);
            showNotification('خطأ في تحميل الطلاب', 'error');
        }
    }

    generateDefaultStudents(grade, section) {
        // إنشاء 20 طالب افتراضي للجدول الفارغ
        const students = [];
        for (let i = 1; i <= 20; i++) {
            students.push({
                id: `temp_${grade}_${section}_${i}`,
                student_number: `${section}${String(i).padStart(3, '0')}`,
                name: `طالب ${i}`,
                grade: grade,
                section: section,
                isTemporary: true
            });
        }
        return students;
    }

    clearTableDisplay() {
        const container = document.getElementById('bulk-upload-container');
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-table"></i>
                </div>
                <h3>اختر الصف والشعبة</h3>
                <p>قم بتحديد الصف والشعبة لإظهار جدول رفع الدرجات</p>
            </div>
        `;
    }

    generateTable() {
        const container = document.getElementById('bulk-upload-container');
        
        let tableHTML = `
            <div class="table-header">
                <h3><i class="fas fa-table"></i> جدول درجات الطلاب</h3>
                <div class="table-info">
                    <span>عدد الطلاب: ${this.currentStudents.length}</span>
                    <span>الصف: ${document.getElementById('bulk-grade-select').value}</span>
                    <span>الشعبة: ${document.getElementById('bulk-section-select').value}</span>
                </div>
            </div>
            
            <div class="table-container">
                <table class="bulk-table" id="bulk-grades-table">
                    <thead>
                        <tr>
                            <th rowspan="2">رقم الطالب</th>
                            <th rowspan="2">اسم الطالب</th>
                            ${this.gradeComponents.map(comp => 
                                `<th colspan="2">${comp.name} (${comp.weight}%)</th>`
                            ).join('')}
                            <th rowspan="2">المجموع</th>
                            <th rowspan="2">النسبة المئوية</th>
                            <th rowspan="2">التقدير</th>
                        </tr>
                        <tr>
                            ${this.gradeComponents.map(() => 
                                '<th>الدرجة</th><th>من</th>'
                            ).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${this.currentStudents.map((student, index) => this.generateStudentRow(student, index)).join('')}
                    </tbody>
                </table>
            </div>
            
            <div class="bulk-stats" id="bulk-stats">
                <div class="bulk-stat-card">
                    <span class="bulk-stat-number" id="total-students">${this.currentStudents.length}</span>
                    <span class="bulk-stat-label">إجمالي الطلاب</span>
                </div>
                <div class="bulk-stat-card">
                    <span class="bulk-stat-number" id="completed-students">0</span>
                    <span class="bulk-stat-label">طلاب مكتملة الدرجات</span>
                </div>
                <div class="bulk-stat-card">
                    <span class="bulk-stat-number" id="average-grade">0.00</span>
                    <span class="bulk-stat-label">المتوسط العام</span>
                </div>
                <div class="bulk-stat-card">
                    <span class="bulk-stat-number" id="modified-cells">0</span>
                    <span class="bulk-stat-label">خلايا معدلة</span>
                </div>
            </div>
        `;

        container.innerHTML = tableHTML;
        this.setupTableEventListeners();
        this.updateStats();
    }

    generateStudentRow(student, index) {
        const isTemporary = student.isTemporary || false;
        const studentNumberClass = isTemporary ? 'student-info editable-cell' : 'student-info';
        const studentNameClass = isTemporary ? 'student-info editable-cell' : 'student-info';

        let rowHTML = `
            <tr data-student-id="${student.id}" data-row-index="${index}" ${isTemporary ? 'data-temporary="true"' : ''}>
                <td class="${studentNumberClass}"
                    ${isTemporary ? 'contenteditable="true" data-field="student_number"' : ''}>${student.student_number}</td>
                <td class="${studentNameClass}"
                    ${isTemporary ? 'contenteditable="true" data-field="name"' : ''}>${student.name}</td>
        `;

        // إضافة خلايا الدرجات لكل مكون
        this.gradeComponents.forEach((comp, compIndex) => {
            rowHTML += `
                <td class="editable-cell"
                    data-student-id="${student.id}"
                    data-component="${compIndex}"
                    data-type="grade"
                    contenteditable="true"
                    data-max="100"
                    placeholder="0">0</td>
                <td class="editable-cell"
                    data-student-id="${student.id}"
                    data-component="${compIndex}"
                    data-type="total"
                    contenteditable="true"
                    data-max="100"
                    placeholder="100">100</td>
            `;
        });

        // خلايا المجموع والنسبة والتقدير (للقراءة فقط)
        rowHTML += `
                <td class="calculated-cell" data-student-id="${student.id}" data-type="sum">0.00</td>
                <td class="calculated-cell" data-student-id="${student.id}" data-type="percentage">0.00%</td>
                <td class="calculated-cell" data-student-id="${student.id}" data-type="grade">-</td>
            </tr>
        `;

        return rowHTML;
    }

    setupTableEventListeners() {
        const table = document.getElementById('bulk-grades-table');
        
        // مستمع تغيير المحتوى
        table.addEventListener('input', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                this.handleCellChange(e.target);
            }
        });

        // مستمع النقر للتحديد
        table.addEventListener('click', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                this.selectCell(e.target);
            }
        });

        // مستمع لمنع الأحرف غير الرقمية
        table.addEventListener('keypress', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                const char = String.fromCharCode(e.which);
                if (!/[0-9.]/.test(char) && e.which !== 8 && e.which !== 46) {
                    e.preventDefault();
                }
            }
        });
    }

    selectCell(cell) {
        // إزالة التحديد السابق
        document.querySelectorAll('.editable-cell.selected').forEach(c => {
            c.classList.remove('selected');
        });

        // تحديد الخلية الحالية
        cell.classList.add('selected');
        this.selectedCell = cell;
    }

    // دالة مساعدة لتحليل البيانات المنسوخة
    parseClipboardData(data) {
        const cleanData = data.trim();

        // إذا كانت البيانات تحتوي على أسطر جديدة
        if (cleanData.includes('\n')) {
            return cleanData.split('\n').map(row => row.trim()).filter(row => row);
        }

        // إذا كانت البيانات تحتوي على تابات (أعمدة متعددة)
        if (cleanData.includes('\t')) {
            return [cleanData];
        }

        // إذا كانت البيانات تحتوي على فواصل
        if (cleanData.includes(',')) {
            const values = cleanData.split(',').map(val => val.trim()).filter(val => val);
            return values;
        }

        // إذا كانت البيانات تحتوي على مسافات متعددة (عمود من Excel منسوخ كنص)
        if (cleanData.includes(' ')) {
            const values = cleanData.split(/\s+/).filter(val => val.trim());
            if (values.length > 1) {
                return values;
            }
        }

        // قيمة واحدة فقط
        return [cleanData];
    }

    handleCellChange(cell) {
        const value = parseFloat(cell.textContent) || 0;
        const max = parseFloat(cell.dataset.max) || 100;

        // التحقق من صحة القيمة
        if (value < 0 || value > max) {
            cell.classList.add('error');
            showNotification(`القيمة يجب أن تكون بين 0 و ${max}`, 'warning');
            return;
        } else {
            cell.classList.remove('error');
        }

        // تمييز الخلية كمعدلة
        cell.classList.add('modified');
        this.modifiedCells.add(cell);

        // إعادة حساب المجموع للطالب
        this.calculateStudentTotal(cell.dataset.studentId);
        this.updateStats();
    }

    calculateStudentTotal(studentId) {
        const row = document.querySelector(`tr[data-student-id="${studentId}"]`);
        let totalWeightedScore = 0;
        let totalMaxScore = 0;

        this.gradeComponents.forEach((comp, index) => {
            const gradeCell = row.querySelector(`[data-component="${index}"][data-type="grade"]`);
            const totalCell = row.querySelector(`[data-component="${index}"][data-type="total"]`);
            
            const grade = parseFloat(gradeCell.textContent) || 0;
            const total = parseFloat(totalCell.textContent) || 100;
            
            const percentage = total > 0 ? (grade / total) * 100 : 0;
            const weightedScore = (percentage * comp.weight) / 100;
            
            totalWeightedScore += weightedScore;
            totalMaxScore += comp.weight;
        });

        const finalPercentage = totalMaxScore > 0 ? totalWeightedScore : 0;
        const gradeLevel = this.getGradeLevel(finalPercentage);

        // تحديث الخلايا المحسوبة
        row.querySelector('[data-type="sum"]').textContent = totalWeightedScore.toFixed(2);
        row.querySelector('[data-type="percentage"]').textContent = finalPercentage.toFixed(2) + '%';
        row.querySelector('[data-type="grade"]').textContent = gradeLevel;
    }

    getGradeLevel(percentage) {
        if (percentage >= 90) return 'ممتاز (أ)';
        if (percentage >= 80) return 'جيد جداً (ب)';
        if (percentage >= 70) return 'جيد (ج)';
        if (percentage >= 50) return 'مقبول (د)';
        return 'ضعيف (هـ)';
    }

    handlePaste(e) {
        if (!this.selectedCell) return;

        e.preventDefault();
        const clipboardData = e.clipboardData || window.clipboardData;
        const pastedData = clipboardData.getData('text');

        if (!pastedData) return;

        console.log('البيانات المنسوخة الأصلية:', JSON.stringify(pastedData));

        const startCell = this.selectedCell;
        const startRow = startCell.closest('tr');
        const startRowIndex = Array.from(startRow.parentNode.children).indexOf(startRow);
        const startCellIndex = Array.from(startRow.children).indexOf(startCell);
        const tableBody = startRow.parentNode;

        let pastedCells = 0;
        let processedData;

        // تحليل البيانات المنسوخة
        if (pastedData.includes('\n')) {
            // بيانات متعددة الصفوف (نسخ من عدة خلايا)
            processedData = pastedData.trim().split('\n').map(row => {
                if (row.includes('\t')) {
                    return row.split('\t');
                } else {
                    return [row.trim()];
                }
            });
        } else if (pastedData.includes('\t')) {
            // بيانات متعددة الأعمدة في صف واحد
            processedData = [pastedData.split('\t')];
        } else {
            // عمود واحد أو قيمة واحدة
            // تحقق من وجود قيم متعددة مفصولة بمسافات أو فواصل
            const cleanData = pastedData.trim();

            if (cleanData.includes('\r\n')) {
                // Windows line endings
                processedData = cleanData.split('\r\n').map(val => [val.trim()]).filter(val => val[0]);
            } else if (cleanData.includes('\r')) {
                // Mac line endings
                processedData = cleanData.split('\r').map(val => [val.trim()]).filter(val => val[0]);
            } else if (cleanData.match(/^\d+(\.\d+)?\s+\d+(\.\d+)?/)) {
                // أرقام مفصولة بمسافات (مثل: "85 92 78")
                processedData = cleanData.split(/\s+/).map(val => [val.trim()]).filter(val => val[0]);
            } else if (cleanData.includes(',')) {
                // قيم مفصولة بفواصل
                processedData = cleanData.split(',').map(val => [val.trim()]).filter(val => val[0]);
            } else {
                // قيمة واحدة فقط
                processedData = [[cleanData]];
            }
        }

        console.log('البيانات بعد المعالجة:', processedData);

        // لصق البيانات في الجدول
        processedData.forEach((rowData, rowOffset) => {
            const targetRow = tableBody.children[startRowIndex + rowOffset];

            if (!targetRow) return;

            rowData.forEach((cellData, cellOffset) => {
                const targetCell = targetRow.children[startCellIndex + cellOffset];

                if (targetCell && targetCell.classList.contains('editable-cell')) {
                    const cleanCellData = cellData.trim();
                    if (cleanCellData && cleanCellData !== '') {
                        targetCell.textContent = cleanCellData;
                        targetCell.classList.add('modified');
                        this.modifiedCells.add(targetCell);
                        this.handleCellChange(targetCell);
                        pastedCells++;
                    }
                }
            });
        });

        if (pastedCells > 0) {
            showNotification(`تم لصق ${pastedCells} خلية بنجاح`, 'success');
            this.updateStats();
        } else {
            showNotification('لم يتم لصق أي بيانات صحيحة', 'warning');
        }
    }

    updateStats() {
        const completedStudents = this.currentStudents.filter(student => {
            const row = document.querySelector(`tr[data-student-id="${student.id}"]`);
            const percentage = parseFloat(row.querySelector('[data-type="percentage"]').textContent) || 0;
            return percentage > 0;
        }).length;

        const totalPercentages = Array.from(document.querySelectorAll('[data-type="percentage"]'))
            .map(cell => parseFloat(cell.textContent) || 0)
            .filter(p => p > 0);

        const averageGrade = totalPercentages.length > 0 
            ? totalPercentages.reduce((sum, p) => sum + p, 0) / totalPercentages.length 
            : 0;

        document.getElementById('completed-students').textContent = completedStudents;
        document.getElementById('average-grade').textContent = averageGrade.toFixed(2);
        document.getElementById('modified-cells').textContent = this.modifiedCells.size;
    }

    async saveAllGrades() {
        if (this.modifiedCells.size === 0) {
            showNotification('لا توجد تغييرات للحفظ', 'warning');
            return;
        }

        const academicYear = document.getElementById('bulk-academic-year').value;
        const semester = document.getElementById('bulk-semester').value;
        const gradeLevel = document.getElementById('bulk-grade-select').value;

        try {
            let savedCount = 0;
            let createdStudents = 0;

            for (const student of this.currentStudents) {
                const row = document.querySelector(`tr[data-student-id="${student.id}"]`);
                let studentId = student.id;

                // إذا كان طالب مؤقت، أنشئه أولاً
                if (student.isTemporary) {
                    const studentNumberCell = row.querySelector('[data-field="student_number"]');
                    const studentNameCell = row.querySelector('[data-field="name"]');

                    const studentNumber = studentNumberCell ? studentNumberCell.textContent.trim() : student.student_number;
                    const studentName = studentNameCell ? studentNameCell.textContent.trim() : student.name;

                    // تحقق من وجود بيانات صحيحة
                    if (studentNumber && studentName && studentName !== 'طالب ' + student.id.split('_').pop()) {
                        try {
                            const newStudent = await dbManager.addStudent({
                                student_number: studentNumber,
                                name: studentName,
                                grade: gradeLevel,
                                section: document.getElementById('bulk-section-select').value
                            });
                            studentId = newStudent.id;
                            createdStudents++;
                        } catch (error) {
                            console.warn('خطأ في إنشاء الطالب:', error);
                            continue; // تخطي هذا الطالب
                        }
                    } else {
                        continue; // تخطي الطلاب الفارغين
                    }
                }

                // حفظ الدرجات
                for (let compIndex = 0; compIndex < this.gradeComponents.length; compIndex++) {
                    const gradeCell = row.querySelector(`[data-component="${compIndex}"][data-type="grade"]`);
                    const totalCell = row.querySelector(`[data-component="${compIndex}"][data-type="total"]`);

                    if (gradeCell.classList.contains('modified') || totalCell.classList.contains('modified')) {
                        const grade = parseFloat(gradeCell.textContent) || 0;
                        const total = parseFloat(totalCell.textContent) || 100;

                        if (grade > 0 || total !== 100) { // حفظ فقط إذا كانت هناك بيانات فعلية
                            await dbManager.saveGrades({
                                student_id: studentId,
                                academic_year: academicYear,
                                semester: parseInt(semester),
                                grade_level: gradeLevel,
                                component_name: this.gradeComponents[compIndex].name,
                                grade: grade,
                                total: total
                            });

                            savedCount++;
                        }
                    }
                }
            }

            // مسح علامات التعديل
            this.modifiedCells.forEach(cell => {
                cell.classList.remove('modified');
            });
            this.modifiedCells.clear();

            let message = `تم حفظ ${savedCount} درجة بنجاح`;
            if (createdStudents > 0) {
                message += `\nتم إنشاء ${createdStudents} طالب جديد`;
            }

            showNotification(message, 'success');
            this.updateStats();

            // إعادة تحميل الجدول بالبيانات الحقيقية
            setTimeout(() => {
                this.loadStudentsTable();
            }, 1000);

        } catch (error) {
            console.error('خطأ في حفظ الدرجات:', error);
            showNotification('خطأ في حفظ الدرجات', 'error');
        }
    }

    clearTable() {
        if (confirm('هل أنت متأكد من مسح جميع البيانات في الجدول؟')) {
            document.querySelectorAll('.editable-cell').forEach(cell => {
                if (cell.dataset.type === 'grade') {
                    cell.textContent = '0';
                } else if (cell.dataset.type === 'total') {
                    cell.textContent = '100';
                }
                cell.classList.remove('modified', 'error');
            });

            this.modifiedCells.clear();
            
            // إعادة حساب جميع المجاميع
            this.currentStudents.forEach(student => {
                this.calculateStudentTotal(student.id);
            });

            this.updateStats();
            showNotification('تم مسح الجدول بنجاح', 'success');
        }
    }

    showInstructions() {
        const instructions = document.getElementById('bulk-instructions');
        instructions.style.display = instructions.style.display === 'none' ? 'block' : 'none';
    }

    showPastePreview() {
        if (!this.selectedCell) return;

        // إظهار مؤشر بصري لمنطقة اللصق
        const cell = this.selectedCell;
        cell.style.boxShadow = '0 0 10px #2196f3';
        cell.style.background = '#e3f2fd';

        setTimeout(() => {
            cell.style.boxShadow = '';
            cell.style.background = '';
        }, 2000);
    }

    // استيراد من ملف نصي
    async importFromTextFile() {
        try {
            // استخدام Electron API لفتح ملف
            if (window.electronAPI) {
                const result = await window.electronAPI.showOpenDialog({
                    title: 'اختر ملف نصي يحتوي على أسماء الطلاب',
                    filters: [
                        { name: 'ملفات نصية', extensions: ['txt'] },
                        { name: 'جميع الملفات', extensions: ['*'] }
                    ],
                    properties: ['openFile']
                });

                if (!result.canceled && result.filePaths.length > 0) {
                    const filePath = result.filePaths[0];
                    const fs = require('fs');
                    const content = fs.readFileSync(filePath, 'utf8');
                    this.processImportedNames(content);
                }
            } else {
                // للمتصفح العادي
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.txt';
                input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            this.processImportedNames(e.target.result);
                        };
                        reader.readAsText(file);
                    }
                };
                input.click();
            }
        } catch (error) {
            console.error('خطأ في استيراد الملف النصي:', error);
            showNotification('خطأ في استيراد الملف النصي', 'error');
        }
    }

    // استيراد من ملف CSV
    async importFromCSV() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.showOpenDialog({
                    title: 'اختر ملف CSV يحتوي على بيانات الطلاب',
                    filters: [
                        { name: 'ملفات CSV', extensions: ['csv'] },
                        { name: 'جميع الملفات', extensions: ['*'] }
                    ],
                    properties: ['openFile']
                });

                if (!result.canceled && result.filePaths.length > 0) {
                    const filePath = result.filePaths[0];
                    const fs = require('fs');
                    const content = fs.readFileSync(filePath, 'utf8');
                    this.processCSVData(content);
                }
            } else {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.csv';
                input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            this.processCSVData(e.target.result);
                        };
                        reader.readAsText(file);
                    }
                };
                input.click();
            }
        } catch (error) {
            console.error('خطأ في استيراد ملف CSV:', error);
            showNotification('خطأ في استيراد ملف CSV', 'error');
        }
    }

    // استيراد من Excel
    async importFromExcel() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.showOpenDialog({
                    title: 'اختر ملف Excel يحتوي على بيانات الطلاب',
                    filters: [
                        { name: 'ملفات Excel', extensions: ['xlsx', 'xls'] },
                        { name: 'جميع الملفات', extensions: ['*'] }
                    ],
                    properties: ['openFile']
                });

                if (!result.canceled && result.filePaths.length > 0) {
                    const filePath = result.filePaths[0];
                    const fs = require('fs');
                    const buffer = fs.readFileSync(filePath);

                    // تحميل مكتبة XLSX إذا لم تكن محملة
                    if (typeof XLSX === 'undefined') {
                        await this.loadXLSXLibrary();
                    }

                    const workbook = XLSX.read(buffer, { type: 'buffer' });
                    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                    const data = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

                    this.processExcelData(data);
                }
            } else {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.xlsx,.xls';
                input.onchange = async (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        if (typeof XLSX === 'undefined') {
                            await this.loadXLSXLibrary();
                        }

                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const data = new Uint8Array(e.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                            const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
                            this.processExcelData(jsonData);
                        };
                        reader.readAsArrayBuffer(file);
                    }
                };
                input.click();
            }
        } catch (error) {
            console.error('خطأ في استيراد ملف Excel:', error);
            showNotification('خطأ في استيراد ملف Excel', 'error');
        }
    }
}

    // تحميل مكتبة XLSX
    loadXLSXLibrary() {
        return new Promise((resolve, reject) => {
            if (typeof XLSX !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            script.onload = () => resolve();
            script.onerror = () => reject(new Error('فشل في تحميل مكتبة Excel'));
            document.head.appendChild(script);
        });
    }

    // معالجة الأسماء المستوردة من ملف نصي
    processImportedNames(content) {
        try {
            const lines = content.trim().split('\n').map(line => line.trim()).filter(line => line);

            if (lines.length === 0) {
                showNotification('الملف فارغ أو لا يحتوي على أسماء صحيحة', 'warning');
                return;
            }

            this.fillStudentNames(lines);
            showNotification(`تم استيراد ${lines.length} اسم بنجاح`, 'success');

        } catch (error) {
            console.error('خطأ في معالجة الأسماء:', error);
            showNotification('خطأ في معالجة الأسماء المستوردة', 'error');
        }
    }

    // معالجة بيانات CSV
    processCSVData(content) {
        try {
            const lines = content.trim().split('\n');
            const students = [];

            lines.forEach((line, index) => {
                const columns = line.split(',').map(col => col.trim().replace(/"/g, ''));

                if (columns.length >= 2 && columns[0] && columns[1]) {
                    students.push({
                        student_number: columns[0],
                        name: columns[1]
                    });
                } else if (columns.length === 1 && columns[0]) {
                    // إذا كان عمود واحد فقط، استخدمه كاسم وأنشئ رقم تلقائي
                    const section = document.getElementById('bulk-section-select').value || 'أ';
                    students.push({
                        student_number: `${section}${String(index + 1).padStart(3, '0')}`,
                        name: columns[0]
                    });
                }
            });

            if (students.length === 0) {
                showNotification('لم يتم العثور على بيانات صحيحة في ملف CSV', 'warning');
                return;
            }

            this.fillStudentData(students);
            showNotification(`تم استيراد ${students.length} طالب من CSV بنجاح`, 'success');

        } catch (error) {
            console.error('خطأ في معالجة CSV:', error);
            showNotification('خطأ في معالجة ملف CSV', 'error');
        }
    }

    // معالجة بيانات Excel
    processExcelData(data) {
        try {
            const students = [];

            // تخطي الصف الأول إذا كان يحتوي على عناوين
            const startRow = (data[0] && (data[0][0] === 'رقم الطالب' || data[0][0] === 'الرقم')) ? 1 : 0;

            for (let i = startRow; i < data.length; i++) {
                const row = data[i];
                if (!row || row.length === 0) continue;

                if (row.length >= 2 && row[0] && row[1]) {
                    students.push({
                        student_number: String(row[0]).trim(),
                        name: String(row[1]).trim()
                    });
                } else if (row.length === 1 && row[0]) {
                    // عمود واحد فقط
                    const section = document.getElementById('bulk-section-select').value || 'أ';
                    students.push({
                        student_number: `${section}${String(i + 1).padStart(3, '0')}`,
                        name: String(row[0]).trim()
                    });
                }
            }

            if (students.length === 0) {
                showNotification('لم يتم العثور على بيانات صحيحة في ملف Excel', 'warning');
                return;
            }

            this.fillStudentData(students);
            showNotification(`تم استيراد ${students.length} طالب من Excel بنجاح`, 'success');

        } catch (error) {
            console.error('خطأ في معالجة Excel:', error);
            showNotification('خطأ في معالجة ملف Excel', 'error');
        }
    }

    // ملء أسماء الطلاب في الجدول
    fillStudentNames(names) {
        const rows = document.querySelectorAll('#bulk-grades-table tbody tr');

        names.forEach((name, index) => {
            if (index < rows.length) {
                const row = rows[index];
                const nameCell = row.querySelector('[data-field="name"]');
                if (nameCell) {
                    nameCell.textContent = name;
                    nameCell.classList.add('modified');
                    this.modifiedCells.add(nameCell);
                }
            }
        });

        this.updateStats();
    }

    // ملء بيانات الطلاب (رقم + اسم)
    fillStudentData(students) {
        const rows = document.querySelectorAll('#bulk-grades-table tbody tr');

        students.forEach((student, index) => {
            if (index < rows.length) {
                const row = rows[index];

                const numberCell = row.querySelector('[data-field="student_number"]');
                const nameCell = row.querySelector('[data-field="name"]');

                if (numberCell && student.student_number) {
                    numberCell.textContent = student.student_number;
                    numberCell.classList.add('modified');
                    this.modifiedCells.add(numberCell);
                }

                if (nameCell && student.name) {
                    nameCell.textContent = student.name;
                    nameCell.classList.add('modified');
                    this.modifiedCells.add(nameCell);
                }
            }
        });

        this.updateStats();
    }

    // نافذة الإدخال السريع
    showQuickEntry() {
        const modal = document.createElement('div');
        modal.className = 'quick-entry-modal';
        modal.innerHTML = `
            <div class="quick-entry-content">
                <div class="quick-entry-header">
                    <h3><i class="fas fa-keyboard"></i> إدخال سريع لأسماء الطلاب</h3>
                    <p>أدخل اسم كل طالب في سطر منفصل</p>
                </div>

                <div class="quick-entry-example">
                    <strong>مثال:</strong><br>
                    أحمد محمد علي<br>
                    فاطمة أحمد سالم<br>
                    محمد علي حسن<br>
                    نور الدين خالد<br>
                    سارة عبدالله
                </div>

                <textarea class="quick-entry-textarea" placeholder="أدخل أسماء الطلاب هنا، كل اسم في سطر منفصل..."></textarea>

                <div class="quick-entry-actions">
                    <button class="btn btn-success" onclick="bulkUploadManager.processQuickEntry()">
                        <i class="fas fa-check"></i> إضافة الأسماء
                    </button>
                    <button class="btn btn-secondary" onclick="bulkUploadManager.closeQuickEntry()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // التركيز على النص
        const textarea = modal.querySelector('.quick-entry-textarea');
        textarea.focus();

        // إغلاق عند النقر خارج النافذة
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeQuickEntry();
            }
        });

        this.quickEntryModal = modal;
    }

    // معالجة الإدخال السريع
    processQuickEntry() {
        const textarea = this.quickEntryModal.querySelector('.quick-entry-textarea');
        const content = textarea.value.trim();

        if (!content) {
            showNotification('يرجى إدخال أسماء الطلاب', 'warning');
            return;
        }

        this.processImportedNames(content);
        this.closeQuickEntry();
    }

    // إغلاق نافذة الإدخال السريع
    closeQuickEntry() {
        if (this.quickEntryModal) {
            document.body.removeChild(this.quickEntryModal);
            this.quickEntryModal = null;
        }
    }
}

// إنشاء مثيل من مدير رفع الدرجات
const bulkUploadManager = new BulkUploadManager();
