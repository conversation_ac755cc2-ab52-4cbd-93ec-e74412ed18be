class BulkUploadManager {
    constructor() {
        this.currentStudents = [];
        this.modifiedCells = new Set();
        this.gradeComponents = [
            { name: 'أعمال السنة', weight: 40 },
            { name: 'اختبار منتصف الفصل', weight: 20 },
            { name: 'اختبار نهاية الفصل', weight: 40 }
        ];
        this.setupEventListeners();
    }

    setupEventListeners() {
        // إعداد مستمعي الأحداث للنسخ واللصق
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'v') {
                this.handlePaste(e);
            }
        });

        // إعداد مستمع للنقر على الخلايا
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                this.selectCell(e.target);
            }
        });

        // إعداد مستمعي تغيير الفلاتر
        document.addEventListener('change', (e) => {
            if (e.target.id === 'bulk-grade-select' || e.target.id === 'bulk-section-select') {
                this.onFiltersChange();
            }
        });
    }

    onFiltersChange() {
        const grade = document.getElementById('bulk-grade-select').value;
        const section = document.getElementById('bulk-section-select').value;

        if (grade && section) {
            // إظهار الجدول تلقائياً عند اختيار الصف والشعبة
            this.loadStudentsTable();
        } else {
            // مسح الجدول إذا لم يتم اختيار الفلاتر
            this.clearTableDisplay();
        }
    }

    async loadStudentsTable() {
        const grade = document.getElementById('bulk-grade-select').value;
        const section = document.getElementById('bulk-section-select').value;

        if (!grade || !section) {
            this.clearTableDisplay();
            return;
        }

        try {
            // تحميل الطلاب
            const students = await dbManager.getStudents({ grade, section });

            if (students.length === 0) {
                // إنشاء طلاب افتراضيين إذا لم يوجدوا
                this.currentStudents = this.generateDefaultStudents(grade, section);
                showNotification(`لا يوجد طلاب مسجلين. تم إنشاء جدول فارغ للصف ${grade} الشعبة ${section}`, 'info');
            } else {
                this.currentStudents = students;
                showNotification(`تم تحميل ${students.length} طالب`, 'success');
            }

            this.generateTable();

        } catch (error) {
            console.error('خطأ في تحميل الطلاب:', error);
            showNotification('خطأ في تحميل الطلاب', 'error');
        }
    }

    generateDefaultStudents(grade, section) {
        // إنشاء 20 طالب افتراضي للجدول الفارغ
        const students = [];
        for (let i = 1; i <= 20; i++) {
            students.push({
                id: `temp_${grade}_${section}_${i}`,
                student_number: `${section}${String(i).padStart(3, '0')}`,
                name: `طالب ${i}`,
                grade: grade,
                section: section,
                isTemporary: true
            });
        }
        return students;
    }

    clearTableDisplay() {
        const container = document.getElementById('bulk-upload-container');
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-table"></i>
                </div>
                <h3>اختر الصف والشعبة</h3>
                <p>قم بتحديد الصف والشعبة لإظهار جدول رفع الدرجات</p>
            </div>
        `;
    }

    generateTable() {
        const container = document.getElementById('bulk-upload-container');
        
        let tableHTML = `
            <div class="table-header">
                <h3><i class="fas fa-table"></i> جدول درجات الطلاب</h3>
                <div class="table-info">
                    <span>عدد الطلاب: ${this.currentStudents.length}</span>
                    <span>الصف: ${document.getElementById('bulk-grade-select').value}</span>
                    <span>الشعبة: ${document.getElementById('bulk-section-select').value}</span>
                </div>
            </div>
            
            <div class="table-container">
                <table class="bulk-table" id="bulk-grades-table">
                    <thead>
                        <tr>
                            <th rowspan="2">رقم الطالب</th>
                            <th rowspan="2">اسم الطالب</th>
                            ${this.gradeComponents.map(comp => 
                                `<th colspan="2">${comp.name} (${comp.weight}%)</th>`
                            ).join('')}
                            <th rowspan="2">المجموع</th>
                            <th rowspan="2">النسبة المئوية</th>
                            <th rowspan="2">التقدير</th>
                        </tr>
                        <tr>
                            ${this.gradeComponents.map(() => 
                                '<th>الدرجة</th><th>من</th>'
                            ).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${this.currentStudents.map((student, index) => this.generateStudentRow(student, index)).join('')}
                    </tbody>
                </table>
            </div>
            
            <div class="bulk-stats" id="bulk-stats">
                <div class="bulk-stat-card">
                    <span class="bulk-stat-number" id="total-students">${this.currentStudents.length}</span>
                    <span class="bulk-stat-label">إجمالي الطلاب</span>
                </div>
                <div class="bulk-stat-card">
                    <span class="bulk-stat-number" id="completed-students">0</span>
                    <span class="bulk-stat-label">طلاب مكتملة الدرجات</span>
                </div>
                <div class="bulk-stat-card">
                    <span class="bulk-stat-number" id="average-grade">0.00</span>
                    <span class="bulk-stat-label">المتوسط العام</span>
                </div>
                <div class="bulk-stat-card">
                    <span class="bulk-stat-number" id="modified-cells">0</span>
                    <span class="bulk-stat-label">خلايا معدلة</span>
                </div>
            </div>
        `;

        container.innerHTML = tableHTML;
        this.setupTableEventListeners();
        this.updateStats();
    }

    generateStudentRow(student, index) {
        const isTemporary = student.isTemporary || false;
        const studentNumberClass = isTemporary ? 'student-info editable-cell' : 'student-info';
        const studentNameClass = isTemporary ? 'student-info editable-cell' : 'student-info';

        let rowHTML = `
            <tr data-student-id="${student.id}" data-row-index="${index}" ${isTemporary ? 'data-temporary="true"' : ''}>
                <td class="${studentNumberClass}"
                    ${isTemporary ? 'contenteditable="true" data-field="student_number"' : ''}>${student.student_number}</td>
                <td class="${studentNameClass}"
                    ${isTemporary ? 'contenteditable="true" data-field="name"' : ''}>${student.name}</td>
        `;

        // إضافة خلايا الدرجات لكل مكون
        this.gradeComponents.forEach((comp, compIndex) => {
            rowHTML += `
                <td class="editable-cell"
                    data-student-id="${student.id}"
                    data-component="${compIndex}"
                    data-type="grade"
                    contenteditable="true"
                    data-max="100"
                    placeholder="0">0</td>
                <td class="editable-cell"
                    data-student-id="${student.id}"
                    data-component="${compIndex}"
                    data-type="total"
                    contenteditable="true"
                    data-max="100"
                    placeholder="100">100</td>
            `;
        });

        // خلايا المجموع والنسبة والتقدير (للقراءة فقط)
        rowHTML += `
                <td class="calculated-cell" data-student-id="${student.id}" data-type="sum">0.00</td>
                <td class="calculated-cell" data-student-id="${student.id}" data-type="percentage">0.00%</td>
                <td class="calculated-cell" data-student-id="${student.id}" data-type="grade">-</td>
            </tr>
        `;

        return rowHTML;
    }

    setupTableEventListeners() {
        const table = document.getElementById('bulk-grades-table');
        
        // مستمع تغيير المحتوى
        table.addEventListener('input', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                this.handleCellChange(e.target);
            }
        });

        // مستمع النقر للتحديد
        table.addEventListener('click', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                this.selectCell(e.target);
            }
        });

        // مستمع لمنع الأحرف غير الرقمية
        table.addEventListener('keypress', (e) => {
            if (e.target.classList.contains('editable-cell')) {
                const char = String.fromCharCode(e.which);
                if (!/[0-9.]/.test(char) && e.which !== 8 && e.which !== 46) {
                    e.preventDefault();
                }
            }
        });
    }

    selectCell(cell) {
        // إزالة التحديد السابق
        document.querySelectorAll('.editable-cell.selected').forEach(c => {
            c.classList.remove('selected');
        });

        // تحديد الخلية الحالية
        cell.classList.add('selected');
        this.selectedCell = cell;
    }

    handleCellChange(cell) {
        const value = parseFloat(cell.textContent) || 0;
        const max = parseFloat(cell.dataset.max) || 100;

        // التحقق من صحة القيمة
        if (value < 0 || value > max) {
            cell.classList.add('error');
            showNotification(`القيمة يجب أن تكون بين 0 و ${max}`, 'warning');
            return;
        } else {
            cell.classList.remove('error');
        }

        // تمييز الخلية كمعدلة
        cell.classList.add('modified');
        this.modifiedCells.add(cell);

        // إعادة حساب المجموع للطالب
        this.calculateStudentTotal(cell.dataset.studentId);
        this.updateStats();
    }

    calculateStudentTotal(studentId) {
        const row = document.querySelector(`tr[data-student-id="${studentId}"]`);
        let totalWeightedScore = 0;
        let totalMaxScore = 0;

        this.gradeComponents.forEach((comp, index) => {
            const gradeCell = row.querySelector(`[data-component="${index}"][data-type="grade"]`);
            const totalCell = row.querySelector(`[data-component="${index}"][data-type="total"]`);
            
            const grade = parseFloat(gradeCell.textContent) || 0;
            const total = parseFloat(totalCell.textContent) || 100;
            
            const percentage = total > 0 ? (grade / total) * 100 : 0;
            const weightedScore = (percentage * comp.weight) / 100;
            
            totalWeightedScore += weightedScore;
            totalMaxScore += comp.weight;
        });

        const finalPercentage = totalMaxScore > 0 ? totalWeightedScore : 0;
        const gradeLevel = this.getGradeLevel(finalPercentage);

        // تحديث الخلايا المحسوبة
        row.querySelector('[data-type="sum"]').textContent = totalWeightedScore.toFixed(2);
        row.querySelector('[data-type="percentage"]').textContent = finalPercentage.toFixed(2) + '%';
        row.querySelector('[data-type="grade"]').textContent = gradeLevel;
    }

    getGradeLevel(percentage) {
        if (percentage >= 90) return 'ممتاز (أ)';
        if (percentage >= 80) return 'جيد جداً (ب)';
        if (percentage >= 70) return 'جيد (ج)';
        if (percentage >= 50) return 'مقبول (د)';
        return 'ضعيف (هـ)';
    }

    handlePaste(e) {
        if (!this.selectedCell) return;

        e.preventDefault();
        const clipboardData = e.clipboardData || window.clipboardData;
        const pastedData = clipboardData.getData('text');

        if (!pastedData) return;

        // تقسيم البيانات إلى صفوف وأعمدة
        const rows = pastedData.trim().split('\n');
        const startCell = this.selectedCell;
        const startRow = startCell.closest('tr');
        const startRowIndex = Array.from(startRow.parentNode.children).indexOf(startRow);
        const startCellIndex = Array.from(startRow.children).indexOf(startCell);

        // لصق البيانات
        rows.forEach((rowData, rowOffset) => {
            const cells = rowData.split('\t');
            const targetRow = startRow.parentNode.children[startRowIndex + rowOffset];
            
            if (!targetRow) return;

            cells.forEach((cellData, cellOffset) => {
                const targetCell = targetRow.children[startCellIndex + cellOffset];
                
                if (targetCell && targetCell.classList.contains('editable-cell')) {
                    targetCell.textContent = cellData.trim();
                    this.handleCellChange(targetCell);
                }
            });
        });

        showNotification('تم لصق البيانات بنجاح', 'success');
    }

    updateStats() {
        const completedStudents = this.currentStudents.filter(student => {
            const row = document.querySelector(`tr[data-student-id="${student.id}"]`);
            const percentage = parseFloat(row.querySelector('[data-type="percentage"]').textContent) || 0;
            return percentage > 0;
        }).length;

        const totalPercentages = Array.from(document.querySelectorAll('[data-type="percentage"]'))
            .map(cell => parseFloat(cell.textContent) || 0)
            .filter(p => p > 0);

        const averageGrade = totalPercentages.length > 0 
            ? totalPercentages.reduce((sum, p) => sum + p, 0) / totalPercentages.length 
            : 0;

        document.getElementById('completed-students').textContent = completedStudents;
        document.getElementById('average-grade').textContent = averageGrade.toFixed(2);
        document.getElementById('modified-cells').textContent = this.modifiedCells.size;
    }

    async saveAllGrades() {
        if (this.modifiedCells.size === 0) {
            showNotification('لا توجد تغييرات للحفظ', 'warning');
            return;
        }

        const academicYear = document.getElementById('bulk-academic-year').value;
        const semester = document.getElementById('bulk-semester').value;
        const gradeLevel = document.getElementById('bulk-grade-select').value;

        try {
            let savedCount = 0;
            let createdStudents = 0;

            for (const student of this.currentStudents) {
                const row = document.querySelector(`tr[data-student-id="${student.id}"]`);
                let studentId = student.id;

                // إذا كان طالب مؤقت، أنشئه أولاً
                if (student.isTemporary) {
                    const studentNumberCell = row.querySelector('[data-field="student_number"]');
                    const studentNameCell = row.querySelector('[data-field="name"]');

                    const studentNumber = studentNumberCell ? studentNumberCell.textContent.trim() : student.student_number;
                    const studentName = studentNameCell ? studentNameCell.textContent.trim() : student.name;

                    // تحقق من وجود بيانات صحيحة
                    if (studentNumber && studentName && studentName !== 'طالب ' + student.id.split('_').pop()) {
                        try {
                            const newStudent = await dbManager.addStudent({
                                student_number: studentNumber,
                                name: studentName,
                                grade: gradeLevel,
                                section: document.getElementById('bulk-section-select').value
                            });
                            studentId = newStudent.id;
                            createdStudents++;
                        } catch (error) {
                            console.warn('خطأ في إنشاء الطالب:', error);
                            continue; // تخطي هذا الطالب
                        }
                    } else {
                        continue; // تخطي الطلاب الفارغين
                    }
                }

                // حفظ الدرجات
                for (let compIndex = 0; compIndex < this.gradeComponents.length; compIndex++) {
                    const gradeCell = row.querySelector(`[data-component="${compIndex}"][data-type="grade"]`);
                    const totalCell = row.querySelector(`[data-component="${compIndex}"][data-type="total"]`);

                    if (gradeCell.classList.contains('modified') || totalCell.classList.contains('modified')) {
                        const grade = parseFloat(gradeCell.textContent) || 0;
                        const total = parseFloat(totalCell.textContent) || 100;

                        if (grade > 0 || total !== 100) { // حفظ فقط إذا كانت هناك بيانات فعلية
                            await dbManager.saveGrades({
                                student_id: studentId,
                                academic_year: academicYear,
                                semester: parseInt(semester),
                                grade_level: gradeLevel,
                                component_name: this.gradeComponents[compIndex].name,
                                grade: grade,
                                total: total
                            });

                            savedCount++;
                        }
                    }
                }
            }

            // مسح علامات التعديل
            this.modifiedCells.forEach(cell => {
                cell.classList.remove('modified');
            });
            this.modifiedCells.clear();

            let message = `تم حفظ ${savedCount} درجة بنجاح`;
            if (createdStudents > 0) {
                message += `\nتم إنشاء ${createdStudents} طالب جديد`;
            }

            showNotification(message, 'success');
            this.updateStats();

            // إعادة تحميل الجدول بالبيانات الحقيقية
            setTimeout(() => {
                this.loadStudentsTable();
            }, 1000);

        } catch (error) {
            console.error('خطأ في حفظ الدرجات:', error);
            showNotification('خطأ في حفظ الدرجات', 'error');
        }
    }

    clearTable() {
        if (confirm('هل أنت متأكد من مسح جميع البيانات في الجدول؟')) {
            document.querySelectorAll('.editable-cell').forEach(cell => {
                if (cell.dataset.type === 'grade') {
                    cell.textContent = '0';
                } else if (cell.dataset.type === 'total') {
                    cell.textContent = '100';
                }
                cell.classList.remove('modified', 'error');
            });

            this.modifiedCells.clear();
            
            // إعادة حساب جميع المجاميع
            this.currentStudents.forEach(student => {
                this.calculateStudentTotal(student.id);
            });

            this.updateStats();
            showNotification('تم مسح الجدول بنجاح', 'success');
        }
    }

    showInstructions() {
        const instructions = document.getElementById('bulk-instructions');
        instructions.style.display = instructions.style.display === 'none' ? 'block' : 'none';
    }
}

// إنشاء مثيل من مدير رفع الدرجات
const bulkUploadManager = new BulkUploadManager();
