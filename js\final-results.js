// إدارة النتائج النهائية
class FinalResultsManager {
    constructor() {
        this.currentResults = [];
        this.gradeStructures = this.initGradeStructures();
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // أحداث الفلاتر
        document.getElementById('final-academic-year')?.addEventListener('change', () => this.clearResults());
        document.getElementById('final-grade-filter')?.addEventListener('change', () => this.clearResults());
        document.getElementById('final-section-filter')?.addEventListener('change', () => this.clearResults());
    }

    initGradeStructures() {
        return {
            elementary: {
                grades: [1, 2, 3, 4],
                maxTotal: 100, // 50 × 2 فصول
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز', percentage: 90 },
                    { min: 80, level: 'ب', description: 'جيد جداً', percentage: 80 },
                    { min: 70, level: 'ج', description: 'جيد', percentage: 70 },
                    { min: 60, level: 'د', description: 'مقبول', percentage: 60 },
                    { min: 0, level: 'هـ', description: 'راسب', percentage: 0 }
                ]
            },
            intermediate: {
                grades: [5, 6, 7, 8, 9, 10],
                maxTotal: 200, // 100 × 2 فصول
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز', percentage: 90 },
                    { min: 80, level: 'ب', description: 'جيد جداً', percentage: 80 },
                    { min: 70, level: 'ج', description: 'جيد', percentage: 70 },
                    { min: 60, level: 'د', description: 'مقبول', percentage: 60 },
                    { min: 0, level: 'هـ', description: 'راسب', percentage: 0 }
                ]
            },
            secondary: {
                grades: [11, 12],
                maxTotal: 200, // 100 × 2 فصول
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز', percentage: 90 },
                    { min: 80, level: 'ب', description: 'جيد جداً', percentage: 80 },
                    { min: 70, level: 'ج', description: 'جيد', percentage: 70 },
                    { min: 60, level: 'د', description: 'مقبول', percentage: 60 },
                    { min: 0, level: 'هـ', description: 'راسب', percentage: 0 }
                ]
            }
        };
    }

    clearResults() {
        this.currentResults = []; // مسح النتائج الحالية
        const container = document.getElementById('final-results-container');
        if (container) {
            container.innerHTML = `
                <div class="no-results-message">
                    <i class="fas fa-info-circle"></i>
                    <p>اختر المعايير أعلاه لعرض النتائج النهائية</p>
                </div>
            `;
        }
    }

    async loadFinalResults() {
        try {
            const academicYear = document.getElementById('final-academic-year').value;
            const grade = document.getElementById('final-grade-filter').value;
            const section = document.getElementById('final-section-filter').value;

            if (!academicYear) {
                showNotification('يرجى اختيار العام الدراسي', 'warning');
                return;
            }

            // تحميل الطلاب
            const studentFilters = {};
            if (grade) studentFilters.grade = grade;
            if (section) studentFilters.section = section;

            const students = await dbManager.getStudents(studentFilters);
            
            if (students.length === 0) {
                this.showNoStudentsMessage();
                return;
            }

            // تحميل درجات الفصل الأول والثاني
            const semester1Grades = await dbManager.getGrades({
                academic_year: academicYear,
                semester: 1,
                grade_level: grade
            });

            const semester2Grades = await dbManager.getGrades({
                academic_year: academicYear,
                semester: 2,
                grade_level: grade
            });

            // حساب النتائج النهائية
            const finalResults = this.calculateFinalResults(students, semester1Grades, semester2Grades);
            
            // عرض النتائج
            this.renderFinalResults(finalResults, academicYear, grade, section);

        } catch (error) {
            console.error('خطأ في تحميل النتائج النهائية:', error);
            showNotification('خطأ في تحميل النتائج النهائية', 'error');
        }
    }

    calculateFinalResults(students, semester1Grades, semester2Grades) {
        const results = [];

        students.forEach(student => {
            const s1Grade = semester1Grades.find(g => g.student_id === student.id);
            const s2Grade = semester2Grades.find(g => g.student_id === student.id);

            const semester1Total = s1Grade ? s1Grade.total : 0;
            const semester2Total = s2Grade ? s2Grade.total : 0;
            const finalTotal = semester1Total + semester2Total;
            const average = finalTotal / 2;

            // تحديد هيكل الدرجات حسب الصف
            let structure;
            if (student.grade >= 1 && student.grade <= 4) {
                structure = this.gradeStructures.elementary;
            } else if (student.grade >= 5 && student.grade <= 10) {
                structure = this.gradeStructures.intermediate;
            } else {
                structure = this.gradeStructures.secondary;
            }

            const percentage = (finalTotal / structure.maxTotal) * 100;
            const gradeInfo = this.getGradeInfo(percentage, structure);

            results.push({
                student: student,
                semester1Total: semester1Total,
                semester2Total: semester2Total,
                finalTotal: finalTotal,
                average: average,
                percentage: percentage,
                grade: gradeInfo.level,
                description: gradeInfo.description,
                maxTotal: structure.maxTotal,
                hasBothSemesters: s1Grade && s2Grade
            });
        });

        // ترتيب النتائج حسب المجموع الكلي (تنازلي)
        results.sort((a, b) => {
            // ترتيب أولاً حسب المجموع الكلي
            if (b.finalTotal !== a.finalTotal) {
                return b.finalTotal - a.finalTotal;
            }
            // في حالة التساوي، ترتيب حسب النسبة المئوية
            if (b.percentage !== a.percentage) {
                return b.percentage - a.percentage;
            }
            // في حالة التساوي الكامل، ترتيب أبجدياً حسب الاسم
            return a.student.name.localeCompare(b.student.name, 'ar');
        });

        // إضافة رقم الترتيب
        results.forEach((result, index) => {
            result.rank = index + 1;
        });

        this.currentResults = results;
        return results;
    }

    getGradeInfo(percentage, structure) {
        for (const grade of structure.gradeScale) {
            if (percentage >= grade.percentage) {
                return grade;
            }
        }
        return structure.gradeScale[structure.gradeScale.length - 1];
    }

    showNoStudentsMessage() {
        const container = document.getElementById('final-results-container');
        container.innerHTML = `
            <div class="no-results-message">
                <i class="fas fa-exclamation-triangle"></i>
                <p>لا توجد طلاب مطابقون للمعايير المحددة</p>
            </div>
        `;
    }

    renderFinalResults(results, academicYear, grade, section) {
        const container = document.getElementById('final-results-container');
        
        // إنشاء رأس النتائج
        let headerInfo = `العام الدراسي: ${academicYear}`;
        if (grade) headerInfo += ` - الصف: ${grade}`;
        if (section) headerInfo += ` - الشعبة: ${section}`;

        let html = `
            <div class="final-results-header">
                <h3>النتائج النهائية</h3>
                <div class="final-results-info">
                    <span>${headerInfo}</span>
                    <span>عدد الطلاب: ${results.length}</span>
                </div>
            </div>
            
            <div class="final-results-table-container">
                <table class="final-results-table">
                    <thead>
                        <tr>
                            <th rowspan="2">الترتيب</th>
                            <th rowspan="2">رقم الطالب</th>
                            <th rowspan="2">اسم الطالب</th>
                            <th rowspan="2">الصف</th>
                            <th rowspan="2">الشعبة</th>
                            <th colspan="2">درجات الفصول</th>
                            <th rowspan="2">المجموع الكلي</th>
                            <th rowspan="2">متوسط الفصلين</th>
                            <th rowspan="2">النسبة المئوية</th>
                            <th rowspan="2">التقدير</th>
                            <th rowspan="2">المستوى</th>
                        </tr>
                        <tr>
                            <th>الفصل الأول</th>
                            <th>الفصل الثاني</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // إضافة صفوف الطلاب
        results.forEach((result, index) => {
            const gradeClass = this.getGradeCSSClass(result.grade);
            const warningClass = !result.hasBothSemesters ? 'incomplete-data' : '';
            
            html += `
                <tr class="${gradeClass} ${warningClass}">
                    <td class="rank-cell">${result.rank}</td>
                    <td>${result.student.student_number}</td>
                    <td class="student-info">${result.student.name}</td>
                    <td>${result.student.grade}</td>
                    <td>${result.student.section}</td>
                    <td class="semester-total">${result.semester1Total.toFixed(2)}</td>
                    <td class="semester-total">${result.semester2Total.toFixed(2)}</td>
                    <td class="final-total">${result.finalTotal.toFixed(2)}</td>
                    <td class="average-cell">${result.average.toFixed(2)}</td>
                    <td class="percentage-cell">${result.percentage.toFixed(1)}%</td>
                    <td class="grade-cell">${result.grade}</td>
                    <td>${result.description}</td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        // إضافة ملخص الإحصائيات
        html += this.generateSummaryStats(results);

        // إضافة رسالة تحذيرية للبيانات غير المكتملة
        const incompleteCount = results.filter(r => !r.hasBothSemesters).length;
        if (incompleteCount > 0) {
            html += `
                <div class="warning-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تنبيه:</strong> يوجد ${incompleteCount} طالب/طالبة لديهم بيانات غير مكتملة (درجات فصل واحد فقط).
                    يرجى التأكد من إدخال درجات الفصلين للحصول على نتائج دقيقة.
                </div>
            `;
        }

        container.innerHTML = html;
        showNotification('تم تحميل النتائج النهائية بنجاح', 'success');
    }

    generateSummaryStats(results) {
        const totalStudents = results.length;
        const studentsWithBothSemesters = results.filter(r => r.hasBothSemesters).length;
        const averageTotal = results.reduce((sum, r) => sum + r.finalTotal, 0) / totalStudents;
        const averagePercentage = results.reduce((sum, r) => sum + r.percentage, 0) / totalStudents;

        // توزيع الدرجات
        const gradeDistribution = {
            'أ': results.filter(r => r.grade === 'أ').length,
            'ب': results.filter(r => r.grade === 'ب').length,
            'ج': results.filter(r => r.grade === 'ج').length,
            'د': results.filter(r => r.grade === 'د').length,
            'هـ': results.filter(r => r.grade === 'هـ').length
        };

        const passCount = results.filter(r => r.grade !== 'هـ').length;
        const passRate = (passCount / totalStudents) * 100;

        return `
            <div class="final-results-summary">
                <div class="summary-stats">
                    <div class="summary-stat">
                        <h4>إجمالي الطلاب</h4>
                        <div class="stat-value">${totalStudents}</div>
                    </div>
                    <div class="summary-stat">
                        <h4>الطلاب مكتملو البيانات</h4>
                        <div class="stat-value">${studentsWithBothSemesters}</div>
                    </div>
                    <div class="summary-stat">
                        <h4>متوسط المجموع الكلي</h4>
                        <div class="stat-value">${averageTotal.toFixed(2)}</div>
                    </div>
                    <div class="summary-stat">
                        <h4>متوسط النسبة المئوية</h4>
                        <div class="stat-value">${averagePercentage.toFixed(1)}%</div>
                    </div>
                    <div class="summary-stat">
                        <h4>نسبة النجاح</h4>
                        <div class="stat-value">${passRate.toFixed(1)}%</div>
                    </div>
                </div>
                
                <h4 style="text-align: center; margin: 1rem 0; color: var(--primary-color);">توزيع التقديرات</h4>
                <div class="grade-distribution-summary">
                    <div class="grade-summary-item grade-summary-a">
                        <div>أ (ممتاز)</div>
                        <div>${gradeDistribution['أ']}</div>
                    </div>
                    <div class="grade-summary-item grade-summary-b">
                        <div>ب (جيد جداً)</div>
                        <div>${gradeDistribution['ب']}</div>
                    </div>
                    <div class="grade-summary-item grade-summary-c">
                        <div>ج (جيد)</div>
                        <div>${gradeDistribution['ج']}</div>
                    </div>
                    <div class="grade-summary-item grade-summary-d">
                        <div>د (مقبول)</div>
                        <div>${gradeDistribution['د']}</div>
                    </div>
                    <div class="grade-summary-item grade-summary-f">
                        <div>هـ (راسب)</div>
                        <div>${gradeDistribution['هـ']}</div>
                    </div>
                </div>
            </div>
        `;
    }

    getGradeCSSClass(grade) {
        const gradeClasses = {
            'أ': 'final-grade-a',
            'ب': 'final-grade-b',
            'ج': 'final-grade-c',
            'د': 'final-grade-d',
            'هـ': 'final-grade-f'
        };
        return gradeClasses[grade] || '';
    }

    exportToExcel() {
        if (this.currentResults.length === 0) {
            showNotification('لا توجد نتائج للتصدير', 'warning');
            return;
        }

        try {
            // إنشاء البيانات للتصدير
            const exportData = [
                ['الترتيب', 'رقم الطالب', 'اسم الطالب', 'الصف', 'الشعبة', 'الفصل الأول', 'الفصل الثاني', 'المجموع الكلي', 'متوسط الفصلين', 'النسبة المئوية', 'التقدير', 'المستوى']
            ];

            this.currentResults.forEach((result) => {
                exportData.push([
                    result.rank,
                    result.student.student_number,
                    result.student.name,
                    result.student.grade,
                    result.student.section,
                    result.semester1Total.toFixed(2),
                    result.semester2Total.toFixed(2),
                    result.finalTotal.toFixed(2),
                    result.average.toFixed(2),
                    result.percentage.toFixed(1) + '%',
                    result.grade,
                    result.description
                ]);
            });

            // إنشاء ملف Excel
            const ws = XLSX.utils.aoa_to_sheet(exportData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'النتائج النهائية');

            // تنسيق الجدول
            const range = XLSX.utils.decode_range(ws['!ref']);
            for (let R = range.s.r; R <= range.e.r; ++R) {
                for (let C = range.s.c; C <= range.e.c; ++C) {
                    const cell_address = XLSX.utils.encode_cell({ c: C, r: R });
                    if (!ws[cell_address]) continue;
                    
                    // تنسيق الرأس
                    if (R === 0) {
                        ws[cell_address].s = {
                            font: { bold: true },
                            fill: { fgColor: { rgb: "2c3e50" } },
                            alignment: { horizontal: "center" }
                        };
                    }
                }
            }

            const fileName = `النتائج_النهائية_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);
            
            showNotification('تم تصدير النتائج النهائية بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير النتائج:', error);
            showNotification('خطأ في تصدير النتائج', 'error');
        }
    }

    printResults(type = 'color') {
        if (this.currentResults.length === 0) {
            showNotification('لا توجد نتائج للطباعة', 'warning');
            return;
        }

        // إنشاء نافذة طباعة جديدة
        const printWindow = window.open('', '_blank');
        const printContent = this.generateFinalResultsPrintContent(type);

        printWindow.document.write(printContent);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            printWindow.focus();
            printWindow.print();
        };

        showNotification(`تم إعداد النتائج النهائية للطباعة ${this.getPrintTypeName(type)}`, 'success');
    }

    generateFinalResultsPrintContent(mode) {
        // التحقق من وجود النتائج
        if (!this.currentResults || this.currentResults.length === 0) {
            return `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>لا توجد نتائج</title>
                </head>
                <body style="font-family: Arial; text-align: center; padding: 50px;">
                    <h2>لا توجد نتائج للطباعة</h2>
                    <p>يرجى تحميل النتائج النهائية أولاً</p>
                </body>
                </html>
            `;
        }

        const settings = app.settings || {};
        const currentDate = new Date();
        const formattedDate = currentDate.toLocaleDateString('ar-EG');
        const formattedTime = currentDate.toLocaleTimeString('ar-EG', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const academicYear = document.getElementById('final-academic-year')?.value || 'غير محدد';
        const grade = document.getElementById('final-grade-filter')?.value || '';
        const section = document.getElementById('final-section-filter')?.value || '';

        // إنشاء محتوى الطباعة
        let printHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>النتائج النهائية - ${grade ? `الصف ${grade}` : 'جميع الصفوف'}</title>
            <style>
                ${this.getFinalResultsPrintStyles(mode)}
            </style>
        </head>
        <body>
            <div class="print-container">
                <!-- رأس الصفحة الاحترافي -->
                <header class="print-header">
                    <div class="header-top">
                        <div class="ministry-info">
                            <h1>${settings.ministry_name || 'وزارة التربية والتعليم'}</h1>
                            <h2>${settings.directorate_name || 'مديرية التربية والتعليم'}</h2>
                            <h3>${settings.school_name || 'مدرسة تقنية المعلومات'}</h3>
                        </div>
                        <div class="date-time">
                            <div class="date">التاريخ: ${formattedDate}</div>
                            <div class="time">الوقت: ${formattedTime}</div>
                        </div>
                    </div>
                    <div class="document-title">
                        <h2>النتائج النهائية لمادة تقنية المعلومات</h2>
                        <div class="document-info">
                            ${grade ? `<span>الصف: ${grade}</span>` : '<span>جميع الصفوف</span>'}
                            ${section ? `<span>الشعبة: ${section}</span>` : '<span>جميع الشعب</span>'}
                            <span>العام الدراسي: ${academicYear}</span>
                        </div>
                    </div>
                </header>

                <!-- محتوى النتائج -->
                <main class="print-content">
                    ${this.generateFinalResultsTable(mode)}
                </main>

                <!-- تذييل الصفحة -->
                <footer class="print-footer">
                    <div class="signatures">
                        <div class="signature-item">
                            <div class="signature-line"></div>
                            <div class="signature-label">المعلم: ${settings.teacher_name || '........................'}</div>
                        </div>
                        <div class="signature-item">
                            <div class="signature-line"></div>
                            <div class="signature-label">المشرف: ${settings.supervisor_name || '........................'}</div>
                        </div>
                        <div class="signature-item">
                            <div class="signature-line"></div>
                            <div class="signature-label">مدير المدرسة: ${settings.principal_name || '........................'}</div>
                        </div>
                    </div>
                </footer>
            </div>
        </body>
        </html>
        `;

        return printHTML;
    }

    getPrintTypeName(type) {
        const names = {
            'color': 'الملونة',
            'bw': 'بالأبيض والأسود',
            'official': 'الرسمية',
            'summary': 'الملخص التنفيذي'
        };
        return names[type] || 'العادية';
    }

    addPrintMetadata(type) {
        const container = document.getElementById('final-results-container');
        if (!container) return;

        const metadata = document.createElement('div');
        metadata.className = 'print-metadata';
        metadata.innerHTML = `
            <div class="print-info-header">
                <div class="print-timestamp">تاريخ الطباعة: ${formatDateArabic(new Date())} - ${formatTime(new Date())}</div>
                <div class="print-type">نوع الطباعة: ${this.getPrintTypeName(type)}</div>
                <div class="print-user">طُبع بواسطة: نظام تقويم تقنية المعلومات</div>
            </div>
        `;

        container.insertBefore(metadata, container.firstChild);
    }

    generateFinalResultsTable(mode) {
        if (!this.currentResults.length) return '<p>لا توجد نتائج للعرض</p>';

        let tableHTML = `
            <table class="final-results-table">
                <thead>
                    <tr>
                        <th class="student-number">الرقم</th>
                        <th class="student-name">اسم الطالب</th>
                        <th class="student-grade">الصف</th>
                        <th class="student-section">الشعبة</th>
                        <th class="semester-header">الفصل الأول</th>
                        <th class="semester-header">الفصل الثاني</th>
                        <th class="total-header">المجموع النهائي</th>
                        <th class="average-header">المعدل</th>
                        <th class="level-header">المستوى</th>
                        <th class="description-header">التقدير النهائي</th>
                    </tr>
                </thead>
                <tbody>
        `;

        this.currentResults.forEach((result, index) => {
            const rowClass = this.getFinalResultRowClass(result.grade, mode);

            tableHTML += `
                <tr class="${rowClass}">
                    <td class="student-number">${result.student.student_number}</td>
                    <td class="student-name">${result.student.name}</td>
                    <td class="student-grade">${result.student.grade}</td>
                    <td class="student-section">${result.student.section}</td>
                    <td class="semester-grade">${result.semester1Total.toFixed(2)}</td>
                    <td class="semester-grade">${result.semester2Total.toFixed(2)}</td>
                    <td class="final-total">${result.finalTotal.toFixed(2)}</td>
                    <td class="final-average">${result.percentage.toFixed(1)}%</td>
                    <td class="final-level">${result.grade}</td>
                    <td class="final-description">${result.description}</td>
                </tr>
            `;
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        return tableHTML;
    }

    getFinalResultRowClass(level, mode) {
        if (mode === 'bw') return '';

        switch(level) {
            case 'أ': case '1': return 'grade-excellent';
            case 'ب': case '2': return 'grade-very-good';
            case 'ج': case '3': return 'grade-good';
            case 'د': case '4': return 'grade-acceptable';
            default: return 'grade-weak';
        }
    }

    getFinalResultsPrintStyles(mode) {
        const baseStyles = `
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            @page {
                size: A4 landscape;
                margin: 1.5cm 1cm;
                direction: rtl;
            }

            body {
                font-family: 'Noto Sans Arabic', 'Arial Unicode MS', Arial, sans-serif;
                font-size: 11pt;
                line-height: 1.3;
                color: #000;
                background: white;
                direction: rtl;
                text-align: right;
            }

            .print-container {
                width: 100%;
                max-width: 100%;
            }

            /* رأس الصفحة الاحترافي */
            .print-header {
                border-bottom: 3px solid #2c3e50;
                margin-bottom: 15px;
                padding-bottom: 10px;
            }

            .header-top {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 10px;
            }

            .ministry-info h1 {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 3px;
            }

            .ministry-info h2 {
                font-size: 14pt;
                font-weight: 600;
                color: #34495e;
                margin-bottom: 2px;
            }

            .ministry-info h3 {
                font-size: 12pt;
                font-weight: 500;
                color: #7f8c8d;
            }

            .date-time {
                text-align: left;
                font-size: 10pt;
                color: #7f8c8d;
                line-height: 1.4;
            }

            .document-title {
                text-align: center;
                border-top: 1px solid #bdc3c7;
                padding-top: 8px;
            }

            .document-title h2 {
                font-size: 14pt;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }

            .document-info {
                font-size: 10pt;
                color: #7f8c8d;
            }

            .document-info span {
                margin: 0 10px;
                padding: 2px 8px;
                background: #ecf0f1;
                border-radius: 3px;
            }

            /* جدول النتائج النهائية */
            .final-results-table {
                width: 100%;
                border-collapse: collapse;
                margin: 10px 0;
                font-size: 9pt;
            }

            .final-results-table th,
            .final-results-table td {
                border: 1px solid #34495e;
                padding: 4px 6px;
                text-align: center;
                vertical-align: middle;
            }

            .final-results-table th {
                background: linear-gradient(135deg, #2c3e50, #34495e);
                color: white;
                font-weight: bold;
                font-size: 9pt;
            }

            .student-name {
                text-align: right !important;
                min-width: 120px;
                max-width: 150px;
            }

            .student-number {
                min-width: 40px;
            }

            .student-grade,
            .student-section {
                min-width: 35px;
            }

            .semester-header,
            .semester-grade {
                min-width: 60px;
                font-weight: 500;
            }

            .total-header,
            .final-total {
                min-width: 70px;
                font-weight: bold;
                background: #ecf0f1 !important;
            }

            .average-header,
            .final-average {
                min-width: 50px;
                font-weight: 500;
            }

            .level-header,
            .final-level {
                min-width: 40px;
                font-weight: bold;
                font-size: 10pt;
            }

            .description-header,
            .final-description {
                min-width: 80px;
                font-weight: 500;
            }

            /* تذييل الصفحة */
            .print-footer {
                margin-top: 20px;
                border-top: 2px solid #2c3e50;
                padding-top: 15px;
            }

            .signatures {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                gap: 20px;
            }

            .signature-item {
                flex: 1;
                text-align: center;
            }

            .signature-line {
                width: 100%;
                height: 1px;
                background: #2c3e50;
                margin-bottom: 5px;
            }

            .signature-label {
                font-size: 10pt;
                font-weight: 500;
                color: #2c3e50;
            }
        `;

        // إضافة ألوان المستويات حسب نوع الطباعة
        let colorStyles = '';
        if (mode === 'color' || mode === 'official') {
            colorStyles = `
                .grade-excellent {
                    background-color: #d4edda !important;
                    color: #155724 !important;
                }
                .grade-very-good {
                    background-color: #d1ecf1 !important;
                    color: #0c5460 !important;
                }
                .grade-good {
                    background-color: #fff3cd !important;
                    color: #856404 !important;
                }
                .grade-acceptable {
                    background-color: #ffeaa7 !important;
                    color: #6c5ce7 !important;
                }
                .grade-weak {
                    background-color: #f8d7da !important;
                    color: #721c24 !important;
                }
            `;
        }

        return baseStyles + colorStyles;
    }

    removePrintMetadata() {
        const metadata = document.querySelector('.print-metadata');
        if (metadata) {
            metadata.remove();
        }
    }
}

// إنشاء مثيل من مدير النتائج النهائية
const finalResultsManager = new FinalResultsManager();
