// إدارة التقارير والإحصائيات
class ReportsManager {
    constructor() {
        this.chartInstances = {};
        this.init();
    }

    init() {
        // تحميل مكتبة Chart.js إذا لم تكن محملة
        this.loadChartJS();
    }

    loadChartJS() {
        if (typeof Chart === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            document.head.appendChild(script);
        }
    }

    async generateSemesterReport() {
        try {
            const reportContent = document.getElementById('report-content');
            if (!reportContent) return;

            // إظهار نافذة اختيار المعايير
            const criteria = await this.showReportCriteriaModal('semester');
            if (!criteria) return;

            // جمع البيانات
            const data = await this.collectSemesterData(criteria);
            
            // إنشاء التقرير
            const reportHTML = this.generateSemesterReportHTML(data, criteria);
            reportContent.innerHTML = reportHTML;

            // إظهار قسم التقارير
            showSection('reports');
            
            showNotification('تم إنشاء التقرير الفصلي بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في إنشاء التقرير الفصلي:', error);
            showNotification('خطأ في إنشاء التقرير الفصلي', 'error');
        }
    }

    async generateAnnualReport() {
        try {
            const reportContent = document.getElementById('report-content');
            if (!reportContent) return;

            // إظهار نافذة اختيار المعايير
            const criteria = await this.showReportCriteriaModal('annual');
            if (!criteria) return;

            // جمع البيانات
            const data = await this.collectAnnualData(criteria);
            
            // إنشاء التقرير
            const reportHTML = this.generateAnnualReportHTML(data, criteria);
            reportContent.innerHTML = reportHTML;

            // إظهار قسم التقارير
            showSection('reports');
            
            showNotification('تم إنشاء التقرير السنوي بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في إنشاء التقرير السنوي:', error);
            showNotification('خطأ في إنشاء التقرير السنوي', 'error');
        }
    }

    showReportCriteriaModal(reportType) {
        return new Promise((resolve) => {
            const modalContent = `
                <div class="modal-header">
                    <h3>${reportType === 'semester' ? 'معايير التقرير الفصلي' : 'معايير التقرير السنوي'}</h3>
                    <button class="btn btn-sm btn-danger" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="report-criteria-form" class="criteria-form">
                    <div class="form-group">
                        <label for="report-academic-year">العام الدراسي:</label>
                        <select id="report-academic-year" required>
                            <option value="2024-2025">2024-2025</option>
                            <option value="2023-2024">2023-2024</option>
                        </select>
                    </div>
                    
                    ${reportType === 'semester' ? `
                    <div class="form-group">
                        <label for="report-semester">الفصل الدراسي:</label>
                        <select id="report-semester" required>
                            <option value="1">الفصل الأول</option>
                            <option value="2">الفصل الثاني</option>
                        </select>
                    </div>
                    ` : ''}
                    
                    <div class="form-group">
                        <label for="report-grade">الصف (اختياري):</label>
                        <select id="report-grade">
                            <option value="">جميع الصفوف</option>
                            <option value="1">الصف الأول</option>
                            <option value="2">الصف الثاني</option>
                            <option value="3">الصف الثالث</option>
                            <option value="4">الصف الرابع</option>
                            <option value="5">الصف الخامس</option>
                            <option value="6">الصف السادس</option>
                            <option value="7">الصف السابع</option>
                            <option value="8">الصف الثامن</option>
                            <option value="9">الصف التاسع</option>
                            <option value="10">الصف العاشر</option>
                            <option value="11">الصف الحادي عشر</option>
                            <option value="12">الصف الثاني عشر</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="report-section">الشعبة (اختياري):</label>
                        <select id="report-section">
                            <option value="">جميع الشعب</option>
                            <option value="أ">أ</option>
                            <option value="ب">ب</option>
                            <option value="ج">ج</option>
                            <option value="د">د</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-alt"></i> إنشاء التقرير
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            `;

            showModal(modalContent);

            document.getElementById('report-criteria-form').addEventListener('submit', (e) => {
                e.preventDefault();
                
                const criteria = {
                    academic_year: document.getElementById('report-academic-year').value,
                    grade: document.getElementById('report-grade').value,
                    section: document.getElementById('report-section').value
                };

                if (reportType === 'semester') {
                    criteria.semester = document.getElementById('report-semester').value;
                }

                closeModal();
                resolve(criteria);
            });
        });
    }

    async collectSemesterData(criteria) {
        const filters = {
            academic_year: criteria.academic_year,
            semester: criteria.semester
        };

        if (criteria.grade) filters.grade_level = criteria.grade;

        const grades = await dbManager.getGrades(filters);
        const students = await dbManager.getStudents({
            grade: criteria.grade,
            section: criteria.section
        });

        const statistics = await dbManager.getStatistics(filters);

        return {
            grades,
            students,
            statistics,
            criteria
        };
    }

    async collectAnnualData(criteria) {
        const semester1Data = await this.collectSemesterData({
            ...criteria,
            semester: '1'
        });

        const semester2Data = await this.collectSemesterData({
            ...criteria,
            semester: '2'
        });

        return {
            semester1: semester1Data,
            semester2: semester2Data,
            criteria
        };
    }

    generateSemesterReportHTML(data, criteria) {
        const { grades, students, statistics } = data;

        return `
            <div class="report-header">
                <div class="report-title">
                    <h2>التقرير الفصلي - مادة تقنية المعلومات</h2>
                    <div class="report-meta">
                        <span>العام الدراسي: ${criteria.academic_year}</span>
                        <span>الفصل الدراسي: ${criteria.semester === '1' ? 'الأول' : 'الثاني'}</span>
                        ${criteria.grade ? `<span>الصف: ${criteria.grade}</span>` : ''}
                        ${criteria.section ? `<span>الشعبة: ${criteria.section}</span>` : ''}
                    </div>
                </div>
            </div>

            <div class="report-summary">
                <h3>ملخص الأداء</h3>
                <div class="stats-overview">
                    <div class="stat-card">
                        <h4>إجمالي الطلاب</h4>
                        <div class="stat-number">${students.length}</div>
                    </div>
                    <div class="stat-card">
                        <h4>الطلاب المقيمون</h4>
                        <div class="stat-number">${grades.length}</div>
                    </div>
                    <div class="stat-card">
                        <h4>المتوسط العام</h4>
                        <div class="stat-number">${this.calculateAverage(grades).toFixed(2)}</div>
                    </div>
                    <div class="stat-card">
                        <h4>نسبة النجاح</h4>
                        <div class="stat-number">${this.calculatePassRate(grades).toFixed(1)}%</div>
                    </div>
                </div>
            </div>

            <div class="grade-distribution-section">
                <h3>توزيع التقديرات</h3>
                ${this.generateGradeDistributionTable(grades)}
            </div>

            <div class="detailed-grades-section">
                <h3>تفاصيل الدرجات</h3>
                ${this.generateDetailedGradesTable(grades, students)}
            </div>
                        <tr class="grade-weak">
                            <td>ضعيف (هـ)</td>
                            <td>${statistics.grade_distribution.weak}</td>
                            <td>${((statistics.grade_distribution.weak / statistics.total_students) * 100).toFixed(1)}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="detailed-grades-section">
                <h3>تفاصيل الدرجات</h3>
                ${this.generateDetailedGradesTable(grades, students)}
            </div>

            <div class="print-footer">
                <div class="footer-info">
                    <div class="report-summary-footer">
                        <p><strong>ملاحظات هامة:</strong></p>
                        <ul>
                            <li>هذا التقرير تم إنشاؤه آلياً بواسطة نظام تقويم تقنية المعلومات</li>
                            <li>جميع البيانات والدرجات مطابقة لسجلات النظام الرسمية</li>
                            <li>في حالة وجود أي استفسار، يرجى مراجعة إدارة المدرسة</li>
                        </ul>
                    </div>
                </div>

                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">معلم المادة</div>
                        <div class="signature-title">الاسم والتوقيع والتاريخ</div>
                        <div class="signature-date">التاريخ: ___/___/______</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">رئيس قسم تقنية المعلومات</div>
                        <div class="signature-title">الاسم والتوقيع والتاريخ</div>
                        <div class="signature-date">التاريخ: ___/___/______</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">مدير المدرسة</div>
                        <div class="signature-title">الاسم والتوقيع والختم</div>
                        <div class="signature-date">التاريخ: ___/___/______</div>
                    </div>
                </div>

                <div class="footer-stamp">
                    <div class="official-stamp">
                        <div class="stamp-border">
                            <div class="stamp-content">
                                <div class="stamp-text">ختم المدرسة الرسمي</div>
                                <div class="stamp-date">${formatDateArabic(new Date())}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="report-actions no-print">
                <div class="print-options-section">
                    <h4><i class="fas fa-print"></i> خيارات الطباعة الاحترافية</h4>
                    <div class="print-buttons-grid">
                        <button class="btn btn-primary print-btn" onclick="reportsManager.printReport('color')">
                            <i class="fas fa-palette"></i> طباعة ملونة عالية الجودة
                        </button>
                        <button class="btn btn-secondary print-btn" onclick="reportsManager.printReport('bw')">
                            <i class="fas fa-adjust"></i> طباعة أبيض وأسود
                        </button>
                        <button class="btn btn-info print-btn" onclick="reportsManager.printReport('official')">
                            <i class="fas fa-stamp"></i> طباعة رسمية مع التوقيعات
                        </button>
                        <button class="btn btn-warning print-btn" onclick="reportsManager.printReport('summary')">
                            <i class="fas fa-list"></i> طباعة ملخص تنفيذي
                        </button>
                    </div>
                </div>

                <div class="export-options-section">
                    <h4><i class="fas fa-download"></i> تصدير التقرير</h4>
                    <div class="export-buttons-grid">
                        <button class="btn btn-success" onclick="reportsManager.exportReportToPDF()">
                            <i class="fas fa-file-pdf"></i> تصدير PDF احترافي
                        </button>
                        <button class="btn btn-warning" onclick="reportsManager.exportReportToExcel()">
                            <i class="fas fa-file-excel"></i> تصدير Excel مفصل
                        </button>
                        <button class="btn btn-info" onclick="reportsManager.exportReportToWord()">
                            <i class="fas fa-file-word"></i> تصدير Word قابل للتعديل
                        </button>
                    </div>
                </div>

                <div class="preview-options-section">
                    <h4><i class="fas fa-eye"></i> معاينة وإعدادات</h4>
                    <div class="preview-buttons-grid">
                        <button class="btn btn-outline-primary" onclick="reportsManager.showPrintPreview()">
                            <i class="fas fa-search"></i> معاينة قبل الطباعة
                        </button>
                        <button class="btn btn-outline-secondary" onclick="reportsManager.togglePrintMode()">
                            <i class="fas fa-toggle-on"></i> وضع الطباعة
                        </button>
                        <button class="btn btn-outline-info" onclick="reportsManager.printSettings()">
                            <i class="fas fa-cog"></i> إعدادات الطباعة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    generateAnnualReportHTML(data, criteria) {
        const { semester1, semester2 } = data;
        
        return `
            <div class="print-header">
                <div class="school-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h1>استمارة تقويم تقنية المعلومات</h1>
                <div class="school-info">التقرير السنوي الشامل - وزارة التربية والتعليم</div>
                <div class="report-info">
                    <span>العام الدراسي: ${criteria.academic_year}</span>
                    <span>تاريخ إنشاء التقرير: ${formatDateArabic(new Date())}</span>
                    <span>الوقت: ${formatTime(new Date())}</span>
                    <span>نوع التقرير: سنوي مقارن</span>
                </div>
                <div class="report-subtitle">
                    تقرير مقارن شامل لأداء الطلاب عبر الفصلين الدراسيين
                    ${criteria.grade ? ` - الصف ${criteria.grade}` : ''}
                    ${criteria.section ? ` - الشعبة ${criteria.section}` : ''}
                </div>
            </div>

            <div class="report-title">
                <h2>التقرير السنوي</h2>
                <div class="report-details">
                    العام الدراسي: ${criteria.academic_year}
                    ${criteria.grade ? ` - الصف: ${criteria.grade}` : ''}
                    ${criteria.section ? ` - الشعبة: ${criteria.section}` : ''}
                </div>
            </div>

            <div class="annual-comparison">
                <h3>مقارنة الفصلين الدراسيين</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>الفصل الأول</th>
                            <th>الفصل الثاني</th>
                            <th>التحسن</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>عدد الطلاب</td>
                            <td>${semester1.statistics.total_students}</td>
                            <td>${semester2.statistics.total_students}</td>
                            <td>${semester2.statistics.total_students - semester1.statistics.total_students}</td>
                        </tr>
                        <tr>
                            <td>المتوسط العام</td>
                            <td>${semester1.statistics.average_score.toFixed(2)}</td>
                            <td>${semester2.statistics.average_score.toFixed(2)}</td>
                            <td>${(semester2.statistics.average_score - semester1.statistics.average_score).toFixed(2)}</td>
                        </tr>
                        <tr>
                            <td>نسبة النجاح</td>
                            <td>${semester1.statistics.pass_rate.toFixed(1)}%</td>
                            <td>${semester2.statistics.pass_rate.toFixed(1)}%</td>
                            <td>${(semester2.statistics.pass_rate - semester1.statistics.pass_rate).toFixed(1)}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="semester-details">
                <div class="semester-section">
                    <h3>الفصل الدراسي الأول</h3>
                    ${this.generateSemesterSummary(semester1)}
                </div>
                
                <div class="semester-section">
                    <h3>الفصل الدراسي الثاني</h3>
                    ${this.generateSemesterSummary(semester2)}
                </div>
            </div>

            <div class="print-footer">
                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">معلم المادة</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">رئيس القسم</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-label">مدير المدرسة</div>
                        <div class="signature-title">الاسم والتوقيع</div>
                    </div>
                </div>
            </div>

            <div class="report-actions no-print">
                <div class="print-options-section">
                    <h4><i class="fas fa-print"></i> خيارات الطباعة الاحترافية</h4>
                    <div class="print-buttons-grid">
                        <button class="btn btn-primary print-btn" onclick="reportsManager.printReport('color')">
                            <i class="fas fa-palette"></i> طباعة ملونة عالية الجودة
                        </button>
                        <button class="btn btn-secondary print-btn" onclick="reportsManager.printReport('bw')">
                            <i class="fas fa-adjust"></i> طباعة أبيض وأسود
                        </button>
                        <button class="btn btn-info print-btn" onclick="reportsManager.printReport('official')">
                            <i class="fas fa-stamp"></i> طباعة رسمية مع التوقيعات
                        </button>
                        <button class="btn btn-warning print-btn" onclick="reportsManager.printReport('summary')">
                            <i class="fas fa-list"></i> طباعة ملخص تنفيذي
                        </button>
                    </div>
                </div>

                <div class="export-options-section">
                    <h4><i class="fas fa-download"></i> تصدير التقرير</h4>
                    <div class="export-buttons-grid">
                        <button class="btn btn-success" onclick="reportsManager.exportReportToPDF()">
                            <i class="fas fa-file-pdf"></i> تصدير PDF احترافي
                        </button>
                        <button class="btn btn-warning" onclick="reportsManager.exportReportToExcel()">
                            <i class="fas fa-file-excel"></i> تصدير Excel مفصل
                        </button>
                        <button class="btn btn-info" onclick="reportsManager.exportReportToWord()">
                            <i class="fas fa-file-word"></i> تصدير Word قابل للتعديل
                        </button>
                    </div>
                </div>

                <div class="preview-options-section">
                    <h4><i class="fas fa-eye"></i> معاينة وإعدادات</h4>
                    <div class="preview-buttons-grid">
                        <button class="btn btn-outline-primary" onclick="reportsManager.showPrintPreview()">
                            <i class="fas fa-search"></i> معاينة قبل الطباعة
                        </button>
                        <button class="btn btn-outline-secondary" onclick="reportsManager.togglePrintMode()">
                            <i class="fas fa-toggle-on"></i> وضع الطباعة
                        </button>
                        <button class="btn btn-outline-info" onclick="reportsManager.printSettings()">
                            <i class="fas fa-cog"></i> إعدادات الطباعة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    generateSemesterSummary(semesterData) {
        const { statistics } = semesterData;
        
        return `
            <div class="semester-summary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-label">إجمالي الطلاب:</span>
                        <span class="stat-value">${statistics.total_students}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">المتوسط العام:</span>
                        <span class="stat-value">${statistics.average_score.toFixed(2)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">نسبة النجاح:</span>
                        <span class="stat-value">${statistics.pass_rate.toFixed(1)}%</span>
                    </div>
                </div>
                
                <div class="grade-breakdown">
                    <div class="grade-item grade-excellent">
                        <span>ممتاز: ${statistics.grade_distribution.excellent}</span>
                    </div>
                    <div class="grade-item grade-very-good">
                        <span>جيد جداً: ${statistics.grade_distribution.very_good}</span>
                    </div>
                    <div class="grade-item grade-good">
                        <span>جيد: ${statistics.grade_distribution.good}</span>
                    </div>
                    <div class="grade-item grade-acceptable">
                        <span>مقبول: ${statistics.grade_distribution.acceptable}</span>
                    </div>
                    <div class="grade-item grade-weak">
                        <span>ضعيف: ${statistics.grade_distribution.weak}</span>
                    </div>
                </div>
            </div>
        `;
    }

    generateDetailedGradesTable(grades, students) {
        if (grades.length === 0) {
            return '<p class="text-center">لا توجد درجات مسجلة</p>';
        }

        let tableHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الطالب</th>
                        <th>المجموع</th>
                        <th>المستوى</th>
                        <th>التقدير</th>
                    </tr>
                </thead>
                <tbody>
        `;

        grades.forEach(grade => {
            const student = students.find(s => s.id === grade.student_id);
            if (student) {
                tableHTML += `
                    <tr class="${this.getGradeCSSClass(grade.level)}">
                        <td>${student.student_number}</td>
                        <td>${student.name}</td>
                        <td>${grade.total.toFixed(2)}</td>
                        <td>${grade.level}</td>
                        <td>${grade.descriptive_phrase}</td>
                    </tr>
                `;
            }
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        return tableHTML;
    }

    getGradeCSSClass(level) {
        if (level === 'أ' || level === '1') return 'grade-excellent';
        if (level === 'ب' || level === '2') return 'grade-very-good';
        if (level === 'ج' || level === '3') return 'grade-good';
        if (level === 'د' || level === '4') return 'grade-acceptable';
        return 'grade-weak';
    }

    async showStatistics() {
        try {
            const statistics = await dbManager.getStatistics();
            const reportContent = document.getElementById('report-content');
            
            const statisticsHTML = `
                <div class="statistics-dashboard">
                    <h2>الإحصائيات والتحليلات</h2>
                    
                    <div class="stats-overview">
                        <div class="stat-card">
                            <h3>إجمالي الطلاب</h3>
                            <div class="stat-number">${statistics.total_students}</div>
                        </div>
                        <div class="stat-card">
                            <h3>المتوسط العام</h3>
                            <div class="stat-number">${statistics.average_score.toFixed(2)}</div>
                        </div>
                        <div class="stat-card">
                            <h3>نسبة النجاح</h3>
                            <div class="stat-number">${statistics.pass_rate.toFixed(1)}%</div>
                        </div>
                    </div>
                    
                    <div class="charts-section">
                        <div class="chart-container">
                            <canvas id="gradeDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            `;
            
            reportContent.innerHTML = statisticsHTML;
            
            // إنشاء الرسم البياني
            this.createGradeDistributionChart(statistics.grade_distribution);
            
            showSection('reports');
            showNotification('تم تحميل الإحصائيات بنجاح', 'success');
            
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
            showNotification('خطأ في تحميل الإحصائيات', 'error');
        }
    }

    async showAdvancedStatistics() {
        try {
            await this.updateDashboardStats();

            const grades = await dbManager.getGrades();
            const students = await dbManager.getStudents();

            const reportContent = document.getElementById('report-content');

            const statisticsHTML = `
                <div class="advanced-statistics-dashboard">
                    <div class="statistics-header">
                        <h2><i class="fas fa-chart-pie"></i> الإحصائيات والتحليلات المتقدمة</h2>
                        <div class="stats-controls">
                            <button class="btn btn-outline-primary" onclick="reportsManager.refreshStatistics()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                            <button class="btn btn-outline-success" onclick="reportsManager.exportStatistics()">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </div>
                    </div>

                    <div class="statistics-summary">
                        <div class="summary-card excellent">
                            <div class="summary-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="summary-info">
                                <h4>الطلاب المتفوقون</h4>
                                <span class="summary-number" id="excellent-count">0</span>
                                <span class="summary-percentage" id="excellent-percentage">0%</span>
                            </div>
                        </div>
                        <div class="summary-card good">
                            <div class="summary-icon">
                                <i class="fas fa-thumbs-up"></i>
                            </div>
                            <div class="summary-info">
                                <h4>الطلاب الناجحون</h4>
                                <span class="summary-number" id="passed-count">0</span>
                                <span class="summary-percentage" id="passed-percentage">0%</span>
                            </div>
                        </div>
                        <div class="summary-card warning">
                            <div class="summary-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="summary-info">
                                <h4>يحتاجون دعم</h4>
                                <span class="summary-number" id="needs-support-count">0</span>
                                <span class="summary-percentage" id="needs-support-percentage">0%</span>
                            </div>
                        </div>
                        <div class="summary-card danger">
                            <div class="summary-icon">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="summary-info">
                                <h4>الطلاب الراسبون</h4>
                                <span class="summary-number" id="failed-count">0</span>
                                <span class="summary-percentage" id="failed-percentage">0%</span>
                            </div>
                        </div>
                    </div>

                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-pie"></i> توزيع التقديرات</h3>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-outline-primary" onclick="reportsManager.toggleChartType('gradeDistribution')">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="gradeDistributionChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-bar"></i> الأداء حسب الصف</h3>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-outline-primary" onclick="reportsManager.toggleChartType('gradeLevel')">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="gradeLevelChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-line"></i> اتجاه الأداء</h3>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-outline-primary" onclick="reportsManager.toggleChartType('performance')">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="performanceTrendChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-area"></i> مقارنة الفصول</h3>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-outline-primary" onclick="reportsManager.toggleChartType('semester')">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="semesterComparisonChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="detailed-analysis">
                        <div class="analysis-card">
                            <h3><i class="fas fa-analytics"></i> التحليل التفصيلي</h3>
                            <div id="detailed-analysis-content">
                                <!-- سيتم ملؤه بالبيانات -->
                            </div>
                        </div>
                    </div>
                </div>
            `;

            reportContent.innerHTML = statisticsHTML;

            // إنشاء الرسوم البيانية
            setTimeout(() => {
                this.createAdvancedCharts(grades, students);
                this.updateStatisticsSummary(grades);
                this.generateDetailedAnalysis(grades, students);
            }, 100);

            showSection('reports');
            showNotification('تم تحميل الإحصائيات المتقدمة بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في عرض الإحصائيات المتقدمة:', error);
            showNotification('خطأ في تحميل الإحصائيات المتقدمة', 'error');
        }
    }

    createGradeDistributionChart(distribution) {
        const ctx = document.getElementById('gradeDistributionChart');
        if (!ctx) return;

        if (this.chartInstances.gradeDistribution) {
            this.chartInstances.gradeDistribution.destroy();
        }

        this.chartInstances.gradeDistribution = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف'],
                datasets: [{
                    data: [
                        distribution.excellent,
                        distribution.very_good,
                        distribution.good,
                        distribution.acceptable,
                        distribution.weak
                    ],
                    backgroundColor: [
                        '#27ae60',
                        '#3498db',
                        '#f39c12',
                        '#e67e22',
                        '#e74c3c'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'توزيع الدرجات'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    async showInteractiveCharts() {
        try {
            const grades = await dbManager.getGrades();
            const students = await dbManager.getStudents();
            const reportContent = document.getElementById('report-content');

            const chartsHTML = `
                <div class="interactive-charts-dashboard">
                    <div class="charts-header">
                        <h2><i class="fas fa-chart-bar"></i> الرسوم البيانية التفاعلية</h2>
                        <div class="charts-controls">
                            <button class="btn btn-outline-primary" onclick="reportsManager.refreshCharts()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                            <button class="btn btn-outline-success" onclick="reportsManager.exportAllCharts()">
                                <i class="fas fa-download"></i> تصدير الكل
                            </button>
                            <button class="btn btn-outline-info" onclick="reportsManager.toggleChartsFullscreen()">
                                <i class="fas fa-expand"></i> ملء الشاشة
                            </button>
                        </div>
                    </div>

                    <div class="charts-tabs">
                        <button class="tab-btn active" onclick="reportsManager.switchChartsTab('overview')">
                            <i class="fas fa-chart-pie"></i> نظرة عامة
                        </button>
                        <button class="tab-btn" onclick="reportsManager.switchChartsTab('detailed')">
                            <i class="fas fa-chart-bar"></i> تفصيلية
                        </button>
                        <button class="tab-btn" onclick="reportsManager.switchChartsTab('comparison')">
                            <i class="fas fa-balance-scale"></i> مقارنات
                        </button>
                        <button class="tab-btn" onclick="reportsManager.switchChartsTab('trends')">
                            <i class="fas fa-chart-line"></i> اتجاهات
                        </button>
                    </div>

                    <div class="charts-content">
                        <div id="overview-charts" class="charts-tab-content active">
                            <div class="charts-grid-large">
                                <div class="chart-card-large">
                                    <div class="chart-header">
                                        <h3>توزيع التقديرات - دائري</h3>
                                        <div class="chart-controls">
                                            <button class="btn btn-sm btn-outline-primary" onclick="reportsManager.switchChartType('gradeDistPie', 'doughnut')">
                                                <i class="fas fa-circle-notch"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="reportsManager.exportChart('gradeDistPie')">
                                                <i class="fas fa-download"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="chart-container-large">
                                        <canvas id="gradeDistPieChart"></canvas>
                                    </div>
                                </div>

                                <div class="chart-card-large">
                                    <div class="chart-header">
                                        <h3>الأداء حسب الصف - أعمدة</h3>
                                        <div class="chart-controls">
                                            <button class="btn btn-sm btn-outline-primary" onclick="reportsManager.switchChartType('gradeLevel', 'line')">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="reportsManager.exportChart('gradeLevel')">
                                                <i class="fas fa-download"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="chart-container-large">
                                        <canvas id="gradeLevelBarChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="detailed-charts" class="charts-tab-content">
                            <div class="charts-grid">
                                <div class="chart-card">
                                    <div class="chart-header">
                                        <h3>توزيع الدرجات - هيستوجرام</h3>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="gradesHistogramChart"></canvas>
                                    </div>
                                </div>

                                <div class="chart-card">
                                    <div class="chart-header">
                                        <h3>الأداء حسب الشعبة</h3>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="sectionPerformanceChart"></canvas>
                                    </div>
                                </div>

                                <div class="chart-card">
                                    <div class="chart-header">
                                        <h3>معدل النجاح حسب الصف</h3>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="passRateByGradeChart"></canvas>
                                    </div>
                                </div>

                                <div class="chart-card">
                                    <div class="chart-header">
                                        <h3>توزيع الطلاب حسب المستوى</h3>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="studentLevelDistChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="comparison-charts" class="charts-tab-content">
                            <div class="charts-grid">
                                <div class="chart-card">
                                    <div class="chart-header">
                                        <h3>مقارنة الفصول - رادار</h3>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="semesterRadarChart"></canvas>
                                    </div>
                                </div>

                                <div class="chart-card">
                                    <div class="chart-header">
                                        <h3>مقارنة الصفوف</h3>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="gradeComparisonChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="trends-charts" class="charts-tab-content">
                            <div class="charts-grid">
                                <div class="chart-card">
                                    <div class="chart-header">
                                        <h3>اتجاه الأداء الشهري</h3>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="monthlyTrendChart"></canvas>
                                    </div>
                                </div>

                                <div class="chart-card">
                                    <div class="chart-header">
                                        <h3>تطور نسبة النجاح</h3>
                                    </div>
                                    <div class="chart-container">
                                        <canvas id="passRateTrendChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            reportContent.innerHTML = chartsHTML;

            // إنشاء جميع الرسوم البيانية
            setTimeout(() => {
                this.createAllInteractiveCharts(grades, students);
            }, 100);

            showSection('reports');
            showNotification('تم تحميل الرسوم البيانية التفاعلية بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في عرض الرسوم البيانية التفاعلية:', error);
            showNotification('خطأ في تحميل الرسوم البيانية', 'error');
        }
    }

    createAllInteractiveCharts(grades, students) {
        // الرسوم البيانية الأساسية
        this.createGradeDistributionPieChart(grades);
        this.createGradeLevelBarChart(grades, students);

        // الرسوم البيانية التفصيلية
        this.createGradesHistogramChart(grades);
        this.createSectionPerformanceChart(grades, students);
        this.createPassRateByGradeChart(grades, students);
        this.createStudentLevelDistChart(grades);

        // رسوم المقارنة
        this.createSemesterRadarChart(grades);
        this.createGradeComparisonChart(grades, students);

        // رسوم الاتجاهات
        this.createMonthlyTrendChart(grades);
        this.createPassRateTrendChart(grades);
    }

    // وظائف الطباعة المتقدمة - نموذج احترافي
    printReport(type = 'color', reportType = 'general') {
        const reportContent = document.getElementById('report-content');
        if (!reportContent || !reportContent.innerHTML.trim()) {
            showNotification('لا يوجد تقرير للطباعة', 'warning');
            return;
        }

        // إنشاء نافذة طباعة جديدة
        const printWindow = window.open('', '_blank');
        const printContent = this.generateReportPrintContent(type, reportType);

        printWindow.document.write(printContent);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            printWindow.focus();
            printWindow.print();
        };

        showNotification(`تم إعداد التقرير للطباعة ${this.getPrintTypeName(type)}`, 'success');
    }

    generateReportPrintContent(mode, reportType) {
        const settings = app.settings || {};
        const currentDate = new Date();
        const formattedDate = currentDate.toLocaleDateString('ar-EG');
        const formattedTime = currentDate.toLocaleTimeString('ar-EG', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const reportContent = document.getElementById('report-content');
        const reportTitle = this.getReportTitle(reportType);

        // إنشاء محتوى الطباعة
        let printHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${reportTitle}</title>
            <style>
                ${this.getReportPrintStyles(mode)}
            </style>
        </head>
        <body>
            <div class="print-container">
                <!-- رأس الصفحة الاحترافي -->
                <header class="print-header">
                    <div class="header-top">
                        <div class="ministry-info">
                            <h1>${settings.ministry_name || 'وزارة التربية والتعليم'}</h1>
                            <h2>${settings.directorate_name || 'مديرية التربية والتعليم'}</h2>
                            <h3>${settings.school_name || 'مدرسة تقنية المعلومات'}</h3>
                        </div>
                        <div class="date-time">
                            <div class="date">التاريخ: ${formattedDate}</div>
                            <div class="time">الوقت: ${formattedTime}</div>
                        </div>
                    </div>
                    <div class="document-title">
                        <h2>${reportTitle}</h2>
                        <div class="document-info">
                            <span>مادة تقنية المعلومات</span>
                            <span>العام الدراسي: ${settings.current_academic_year || '2024-2025'}</span>
                        </div>
                    </div>
                </header>

                <!-- محتوى التقرير -->
                <main class="print-content">
                    ${this.cleanReportContentForPrint(reportContent.innerHTML)}
                </main>

                <!-- تذييل الصفحة -->
                <footer class="print-footer">
                    <div class="signatures">
                        <div class="signature-item">
                            <div class="signature-line"></div>
                            <div class="signature-label">المعلم: ${settings.teacher_name || '........................'}</div>
                        </div>
                        <div class="signature-item">
                            <div class="signature-line"></div>
                            <div class="signature-label">المشرف: ${settings.supervisor_name || '........................'}</div>
                        </div>
                        <div class="signature-item">
                            <div class="signature-line"></div>
                            <div class="signature-label">مدير المدرسة: ${settings.principal_name || '........................'}</div>
                        </div>
                    </div>
                </footer>
            </div>
        </body>
        </html>
        `;

        return printHTML;
    }

    getReportTitle(reportType) {
        const titles = {
            'semester': 'التقرير الفصلي',
            'annual': 'التقرير السنوي',
            'statistics': 'تقرير الإحصائيات والتحليلات',
            'general': 'تقرير عام'
        };
        return titles[reportType] || 'تقرير';
    }

    cleanReportContentForPrint(content) {
        // إزالة العناصر غير المطلوبة في الطباعة
        let cleanContent = content;

        // إزالة الأزرار والعناصر التفاعلية
        cleanContent = cleanContent.replace(/<button[^>]*>.*?<\/button>/gi, '');
        cleanContent = cleanContent.replace(/<input[^>]*>/gi, '');
        cleanContent = cleanContent.replace(/class="[^"]*no-print[^"]*"/gi, '');

        // تنظيف الجداول للطباعة
        cleanContent = cleanContent.replace(/class="table"/gi, 'class="print-table"');

        return cleanContent;
    }

    getPrintTypeName(type) {
        const names = {
            'color': 'الملونة',
            'bw': 'بالأبيض والأسود',
            'official': 'الرسمية',
            'summary': 'الملخص التنفيذي'
        };
        return names[type] || 'العادية';
    }

    getReportPrintStyles(mode) {
        const baseStyles = `
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            @page {
                size: A4;
                margin: 1.5cm 1cm;
                direction: rtl;
            }

            body {
                font-family: 'Noto Sans Arabic', 'Arial Unicode MS', Arial, sans-serif;
                font-size: 11pt;
                line-height: 1.4;
                color: #000;
                background: white;
                direction: rtl;
                text-align: right;
            }

            .print-container {
                width: 100%;
                max-width: 100%;
            }

            /* رأس الصفحة الاحترافي */
            .print-header {
                border-bottom: 3px solid #2c3e50;
                margin-bottom: 20px;
                padding-bottom: 15px;
            }

            .header-top {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 15px;
            }

            .ministry-info h1 {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 4px;
            }

            .ministry-info h2 {
                font-size: 14pt;
                font-weight: 600;
                color: #34495e;
                margin-bottom: 3px;
            }

            .ministry-info h3 {
                font-size: 12pt;
                font-weight: 500;
                color: #7f8c8d;
            }

            .date-time {
                text-align: left;
                font-size: 10pt;
                color: #7f8c8d;
                line-height: 1.5;
            }

            .document-title {
                text-align: center;
                border-top: 1px solid #bdc3c7;
                padding-top: 10px;
            }

            .document-title h2 {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 8px;
            }

            .document-info {
                font-size: 11pt;
                color: #7f8c8d;
            }

            .document-info span {
                margin: 0 15px;
                padding: 3px 10px;
                background: #ecf0f1;
                border-radius: 4px;
            }

            /* محتوى التقرير */
            .print-content {
                margin: 20px 0;
                min-height: 400px;
            }

            .print-content h2,
            .print-content h3 {
                color: #2c3e50;
                margin-bottom: 15px;
                border-bottom: 2px solid #ecf0f1;
                padding-bottom: 8px;
            }

            .print-content h2 {
                font-size: 14pt;
            }

            .print-content h3 {
                font-size: 12pt;
            }

            /* جداول التقارير */
            .print-table,
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 15px 0;
                font-size: 10pt;
            }

            .print-table th,
            .print-table td,
            table th,
            table td {
                border: 1px solid #34495e;
                padding: 8px 6px;
                text-align: center;
                vertical-align: middle;
            }

            .print-table th,
            table th {
                background: linear-gradient(135deg, #2c3e50, #34495e);
                color: white;
                font-weight: bold;
                font-size: 10pt;
            }

            /* إحصائيات */
            .stats-overview {
                display: flex;
                justify-content: space-around;
                margin: 20px 0;
                flex-wrap: wrap;
            }

            .stat-card {
                text-align: center;
                padding: 15px;
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                margin: 5px;
                min-width: 120px;
            }

            .stat-card h3 {
                font-size: 11pt;
                color: #2c3e50;
                margin-bottom: 8px;
                border: none;
                padding: 0;
            }

            .stat-number {
                font-size: 18pt;
                font-weight: bold;
                color: #3498db;
            }

            /* تذييل الصفحة */
            .print-footer {
                margin-top: 30px;
                border-top: 2px solid #2c3e50;
                padding-top: 20px;
            }

            .signatures {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                gap: 30px;
            }

            .signature-item {
                flex: 1;
                text-align: center;
            }

            .signature-line {
                width: 100%;
                height: 1px;
                background: #2c3e50;
                margin-bottom: 8px;
            }

            .signature-label {
                font-size: 10pt;
                font-weight: 500;
                color: #2c3e50;
            }

            /* إخفاء العناصر غير المطلوبة */
            .no-print,
            button,
            input,
            .btn,
            .action-buttons {
                display: none !important;
            }
        `;

        // إضافة ألوان حسب نوع الطباعة
        let colorStyles = '';
        if (mode === 'color' || mode === 'official') {
            colorStyles = `
                .grade-excellent,
                .level-excellent {
                    background-color: #d4edda !important;
                    color: #155724 !important;
                }
                .grade-very-good,
                .level-very-good {
                    background-color: #d1ecf1 !important;
                    color: #0c5460 !important;
                }
                .grade-good,
                .level-good {
                    background-color: #fff3cd !important;
                    color: #856404 !important;
                }
                .grade-acceptable,
                .level-acceptable {
                    background-color: #ffeaa7 !important;
                    color: #6c5ce7 !important;
                }
                .grade-weak,
                .level-weak {
                    background-color: #f8d7da !important;
                    color: #721c24 !important;
                }
            `;
        }

        return baseStyles + colorStyles;
    }

    // دوال مساعدة للإحصائيات المتقدمة
    generateMonthlyPerformanceData(grades) {
        // محاكاة بيانات شهرية (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
        const months = ['سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو'];
        const averages = [];
        const passRates = [];

        for (let i = 0; i < months.length; i++) {
            // محاكاة تحسن تدريجي في الأداء
            const baseAverage = 65 + (i * 2) + (Math.random() * 10 - 5);
            const basePassRate = 70 + (i * 1.5) + (Math.random() * 8 - 4);

            averages.push(Math.min(95, Math.max(50, baseAverage)));
            passRates.push(Math.min(100, Math.max(60, basePassRate)));
        }

        return {
            labels: months,
            averages: averages,
            passRates: passRates
        };
    }

    updateStatisticsSummary(grades) {
        const distribution = this.getGradeDistribution(grades);
        const total = grades.length;

        // تحديث العدادات
        document.getElementById('excellent-count').textContent = distribution.excellent;
        document.getElementById('excellent-percentage').textContent =
            total > 0 ? `${((distribution.excellent / total) * 100).toFixed(1)}%` : '0%';

        const passed = distribution.excellent + distribution.very_good + distribution.good + distribution.acceptable;
        document.getElementById('passed-count').textContent = passed;
        document.getElementById('passed-percentage').textContent =
            total > 0 ? `${((passed / total) * 100).toFixed(1)}%` : '0%';

        const needsSupport = distribution.acceptable;
        document.getElementById('needs-support-count').textContent = needsSupport;
        document.getElementById('needs-support-percentage').textContent =
            total > 0 ? `${((needsSupport / total) * 100).toFixed(1)}%` : '0%';

        document.getElementById('failed-count').textContent = distribution.weak;
        document.getElementById('failed-percentage').textContent =
            total > 0 ? `${((distribution.weak / total) * 100).toFixed(1)}%` : '0%';
    }

    generateDetailedAnalysis(grades, students) {
        const analysisContent = document.getElementById('detailed-analysis-content');
        if (!analysisContent) return;

        const distribution = this.getGradeDistribution(grades);
        const average = this.calculateAverage(grades);
        const passRate = this.calculatePassRate(grades);

        let analysis = `
            <div class="analysis-grid">
                <div class="analysis-item">
                    <h4><i class="fas fa-chart-line"></i> تحليل الأداء العام</h4>
                    <p>المتوسط العام للطلاب هو <strong>${average.toFixed(2)}</strong> درجة،
                    مما يشير إلى أداء ${this.getPerformanceLevel(average)}.</p>
                </div>

                <div class="analysis-item">
                    <h4><i class="fas fa-percentage"></i> تحليل نسبة النجاح</h4>
                    <p>نسبة النجاح الحالية <strong>${passRate.toFixed(1)}%</strong>
                    ${this.getPassRateAnalysis(passRate)}.</p>
                </div>

                <div class="analysis-item">
                    <h4><i class="fas fa-star"></i> تحليل التفوق</h4>
                    <p>يحقق <strong>${distribution.excellent}</strong> طالب تقدير ممتاز
                    (${((distribution.excellent / grades.length) * 100).toFixed(1)}% من إجمالي الطلاب).</p>
                </div>

                <div class="analysis-item">
                    <h4><i class="fas fa-exclamation-triangle"></i> الطلاب المحتاجون للدعم</h4>
                    <p>هناك <strong>${distribution.weak + distribution.acceptable}</strong> طالب
                    يحتاجون إلى دعم إضافي لتحسين أدائهم.</p>
                </div>

                <div class="analysis-item">
                    <h4><i class="fas fa-lightbulb"></i> التوصيات</h4>
                    <div class="recommendations">
                        ${this.generateRecommendations(distribution, average, passRate)}
                    </div>
                </div>
            </div>
        `;

        analysisContent.innerHTML = analysis;
    }

    getPerformanceLevel(average) {
        if (average >= 90) return 'ممتاز';
        if (average >= 80) return 'جيد جداً';
        if (average >= 70) return 'جيد';
        if (average >= 60) return 'مقبول';
        return 'يحتاج تحسين';
    }

    getPassRateAnalysis(passRate) {
        if (passRate >= 90) return 'وهي نسبة ممتازة تدل على فعالية التدريس';
        if (passRate >= 80) return 'وهي نسبة جيدة جداً';
        if (passRate >= 70) return 'وهي نسبة مقبولة لكن تحتاج تحسين';
        if (passRate >= 60) return 'وهي نسبة منخفضة تحتاج إلى تدخل فوري';
        return 'وهي نسبة ضعيفة جداً تتطلب مراجعة شاملة للمنهج والطرق';
    }

    generateRecommendations(distribution, average, passRate) {
        let recommendations = [];

        if (passRate < 70) {
            recommendations.push('<li><i class="fas fa-arrow-right"></i> مراجعة طرق التدريس وتطوير استراتيجيات جديدة</li>');
        }

        if (distribution.weak > distribution.excellent) {
            recommendations.push('<li><i class="fas fa-arrow-right"></i> تنظيم حصص دعم إضافية للطلاب الضعاف</li>');
        }

        if (distribution.excellent < (distribution.good + distribution.very_good)) {
            recommendations.push('<li><i class="fas fa-arrow-right"></i> تطوير برامج إثرائية للطلاب المتفوقين</li>');
        }

        if (average < 75) {
            recommendations.push('<li><i class="fas fa-arrow-right"></i> إعادة تقييم صعوبة المنهج والامتحانات</li>');
        }

        if (recommendations.length === 0) {
            recommendations.push('<li><i class="fas fa-check"></i> الأداء العام جيد، استمر في نفس النهج</li>');
        }

        return `<ul class="recommendations-list">${recommendations.join('')}</ul>`;
    }

    async updateDashboardStats() {
        try {
            const filters = this.getCurrentFilters();
            const students = await dbManager.getStudents(filters);
            const grades = await dbManager.getGrades(filters);

            // تحديث إحصائيات لوحة التحكم
            document.getElementById('reports-total-students').textContent = students.length;
            document.getElementById('reports-average').textContent = this.calculateAverage(grades).toFixed(2);
            document.getElementById('reports-pass-rate').textContent = `${this.calculatePassRate(grades).toFixed(1)}%`;
            document.getElementById('reports-generated').textContent = this.getGeneratedReportsCount();

        } catch (error) {
            console.error('خطأ في تحديث إحصائيات لوحة التحكم:', error);
        }
    }

    getCurrentFilters() {
        return {
            academic_year: document.getElementById('report-academic-year')?.value || '2024-2025',
            grade: document.getElementById('report-grade-filter')?.value || '',
            section: document.getElementById('report-section-filter')?.value || ''
        };
    }

    getGeneratedReportsCount() {
        // في التطبيق الحقيقي، سيتم حفظ هذا في localStorage أو قاعدة البيانات
        return localStorage.getItem('generated_reports_count') || 0;
    }

    incrementGeneratedReportsCount() {
        const current = parseInt(this.getGeneratedReportsCount()) + 1;
        localStorage.setItem('generated_reports_count', current);
        document.getElementById('reports-generated').textContent = current;
    }

    // دوال مساعدة للحسابات
    calculateAverage(grades) {
        if (!grades || grades.length === 0) return 0;
        const total = grades.reduce((sum, grade) => sum + (grade.total || 0), 0);
        return total / grades.length;
    }

    calculatePassRate(grades) {
        if (!grades || grades.length === 0) return 0;
        const passedStudents = grades.filter(grade => (grade.total || 0) >= 50).length;
        return (passedStudents / grades.length) * 100;
    }

    getGradeDistribution(grades) {
        const distribution = {
            excellent: 0,
            very_good: 0,
            good: 0,
            acceptable: 0,
            weak: 0
        };

        grades.forEach(grade => {
            const total = grade.total || 0;
            if (total >= 90) distribution.excellent++;
            else if (total >= 80) distribution.very_good++;
            else if (total >= 70) distribution.good++;
            else if (total >= 50) distribution.acceptable++;
            else distribution.weak++;
        });

        return distribution;
    }

    // دوال الرسوم البيانية المتقدمة
    createAdvancedCharts(grades, students) {
        this.createGradeDistributionChart(this.getGradeDistribution(grades));
        this.createGradeLevelChart(grades, students);
        this.createPerformanceTrendChart(grades);
        this.createSemesterComparisonChart(grades);
    }

    createGradeLevelChart(grades, students) {
        const ctx = document.getElementById('gradeLevelChart');
        if (!ctx) return;

        if (this.chartInstances.gradeLevel) {
            this.chartInstances.gradeLevel.destroy();
        }

        // تجميع البيانات حسب الصف
        const gradesByLevel = {};
        students.forEach(student => {
            if (!gradesByLevel[student.grade]) {
                gradesByLevel[student.grade] = [];
            }
        });

        grades.forEach(grade => {
            const student = students.find(s => s.id === grade.student_id);
            if (student) {
                if (!gradesByLevel[student.grade]) {
                    gradesByLevel[student.grade] = [];
                }
                gradesByLevel[student.grade].push(grade.total || 0);
            }
        });

        const labels = Object.keys(gradesByLevel).sort();
        const averages = labels.map(level => {
            const levelGrades = gradesByLevel[level];
            return levelGrades.length > 0
                ? levelGrades.reduce((sum, grade) => sum + grade, 0) / levelGrades.length
                : 0;
        });

        this.chartInstances.gradeLevel = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels.map(l => `الصف ${l}`),
                datasets: [{
                    label: 'متوسط الدرجات',
                    data: averages,
                    backgroundColor: [
                        '#3498db', '#e74c3c', '#2ecc71', '#f39c12',
                        '#9b59b6', '#1abc9c', '#34495e', '#e67e22'
                    ],
                    borderColor: '#2c3e50',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'متوسط الأداء حسب الصف الدراسي'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'متوسط الدرجات'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'الصف الدراسي'
                        }
                    }
                }
            }
        });
    }

    createPerformanceTrendChart(grades) {
        const ctx = document.getElementById('performanceTrendChart');
        if (!ctx) return;

        if (this.chartInstances.performanceTrend) {
            this.chartInstances.performanceTrend.destroy();
        }

        // تجميع البيانات حسب الشهر (محاكاة)
        const monthlyData = this.generateMonthlyPerformanceData(grades);

        this.chartInstances.performanceTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: monthlyData.labels,
                datasets: [{
                    label: 'متوسط الأداء',
                    data: monthlyData.averages,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'نسبة النجاح',
                    data: monthlyData.passRates,
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    fill: false,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'اتجاه الأداء عبر الوقت'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'النسبة المئوية'
                        }
                    }
                }
            }
        });
    }

    createSemesterComparisonChart(grades) {
        const ctx = document.getElementById('semesterComparisonChart');
        if (!ctx) return;

        if (this.chartInstances.semesterComparison) {
            this.chartInstances.semesterComparison.destroy();
        }

        // تجميع البيانات حسب الفصل
        const semester1Grades = grades.filter(g => g.semester === 1);
        const semester2Grades = grades.filter(g => g.semester === 2);

        const semester1Dist = this.getGradeDistribution(semester1Grades);
        const semester2Dist = this.getGradeDistribution(semester2Grades);

        this.chartInstances.semesterComparison = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف'],
                datasets: [{
                    label: 'الفصل الأول',
                    data: [
                        semester1Dist.excellent,
                        semester1Dist.very_good,
                        semester1Dist.good,
                        semester1Dist.acceptable,
                        semester1Dist.weak
                    ],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.2)',
                    pointBackgroundColor: '#3498db'
                }, {
                    label: 'الفصل الثاني',
                    data: [
                        semester2Dist.excellent,
                        semester2Dist.very_good,
                        semester2Dist.good,
                        semester2Dist.acceptable,
                        semester2Dist.weak
                    ],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.2)',
                    pointBackgroundColor: '#e74c3c'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'مقارنة توزيع التقديرات بين الفصلين'
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'عدد الطلاب'
                        }
                    }
                }
            }
        });
    }

    generateGradeDistributionTable(grades) {
        const distribution = this.getGradeDistribution(grades);
        const total = grades.length;

        return `
            <table class="print-table">
                <thead>
                    <tr>
                        <th>التقدير</th>
                        <th>المدى</th>
                        <th>عدد الطلاب</th>
                        <th>النسبة المئوية</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="grade-excellent">
                        <td>ممتاز (أ)</td>
                        <td>90 - 100</td>
                        <td>${distribution.excellent}</td>
                        <td>${total > 0 ? ((distribution.excellent / total) * 100).toFixed(1) : 0}%</td>
                    </tr>
                    <tr class="grade-very-good">
                        <td>جيد جداً (ب)</td>
                        <td>80 - 89</td>
                        <td>${distribution.very_good}</td>
                        <td>${total > 0 ? ((distribution.very_good / total) * 100).toFixed(1) : 0}%</td>
                    </tr>
                    <tr class="grade-good">
                        <td>جيد (ج)</td>
                        <td>70 - 79</td>
                        <td>${distribution.good}</td>
                        <td>${total > 0 ? ((distribution.good / total) * 100).toFixed(1) : 0}%</td>
                    </tr>
                    <tr class="grade-acceptable">
                        <td>مقبول (د)</td>
                        <td>50 - 69</td>
                        <td>${distribution.acceptable}</td>
                        <td>${total > 0 ? ((distribution.acceptable / total) * 100).toFixed(1) : 0}%</td>
                    </tr>
                    <tr class="grade-weak">
                        <td>ضعيف (هـ)</td>
                        <td>أقل من 50</td>
                        <td>${distribution.weak}</td>
                        <td>${total > 0 ? ((distribution.weak / total) * 100).toFixed(1) : 0}%</td>
                    </tr>
                </tbody>
            </table>
        `;
    }

    generateDetailedGradesTable(grades, students) {
        if (!grades || grades.length === 0) {
            return '<p class="no-data">لا توجد درجات متاحة</p>';
        }

        let tableHTML = `
            <table class="print-table">
                <thead>
                    <tr>
                        <th>رقم الطالب</th>
                        <th>اسم الطالب</th>
                        <th>الدرجة</th>
                        <th>التقدير</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
        `;

        grades.forEach(grade => {
            const student = students.find(s => s.id === grade.student_id);
            const studentName = student ? student.name : 'غير معروف';
            const studentNumber = student ? student.student_number : 'غير معروف';
            const total = grade.total || 0;
            const gradeLevel = this.getGradeLevel(total);
            const status = total >= 50 ? 'ناجح' : 'راسب';
            const rowClass = this.getGradeRowClass(total);

            tableHTML += `
                <tr class="${rowClass}">
                    <td>${studentNumber}</td>
                    <td>${studentName}</td>
                    <td>${total.toFixed(2)}</td>
                    <td>${gradeLevel}</td>
                    <td>${status}</td>
                </tr>
            `;
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        return tableHTML;
    }

    getGradeLevel(total) {
        if (total >= 90) return 'ممتاز (أ)';
        if (total >= 80) return 'جيد جداً (ب)';
        if (total >= 70) return 'جيد (ج)';
        if (total >= 50) return 'مقبول (د)';
        return 'ضعيف (هـ)';
    }

    getGradeRowClass(total) {
        if (total >= 90) return 'grade-excellent';
        if (total >= 80) return 'grade-very-good';
        if (total >= 70) return 'grade-good';
        if (total >= 50) return 'grade-acceptable';
        return 'grade-weak';
    }

    addPrintMetadata(type) {
        // إضافة معلومات إضافية للطباعة
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const metadata = document.createElement('div');
        metadata.className = 'print-metadata';
        metadata.innerHTML = `
            <div class="print-info-header">
                <div class="print-timestamp">تاريخ الطباعة: ${formatDateArabic(new Date())} - ${formatTime(new Date())}</div>
                <div class="print-type">نوع الطباعة: ${this.getPrintTypeName(type)}</div>
                <div class="print-user">طُبع بواسطة: نظام تقويم تقنية المعلومات</div>
            </div>
        `;

        reportContent.insertBefore(metadata, reportContent.firstChild);
    }

    removePrintMetadata() {
        const metadata = document.querySelector('.print-metadata');
        if (metadata) {
            metadata.remove();
        }
    }

    showPrintPreview() {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) {
            showNotification('لا يوجد تقرير لمعاينته', 'warning');
            return;
        }

        // إنشاء نافذة معاينة
        const previewWindow = window.open('', '_blank', 'width=1000,height=700,scrollbars=yes');

        const previewHTML = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>معاينة التقرير</title>
                <link rel="stylesheet" href="css/styles.css">
                <link rel="stylesheet" href="css/print.css">
                <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                <style>
                    body {
                        background: white;
                        padding: 2cm;
                        font-family: 'Noto Sans Arabic', Arial, sans-serif;
                    }
                    .preview-header {
                        text-align: center;
                        padding: 1rem;
                        background: #f8f9fa;
                        border: 2px solid #dee2e6;
                        border-radius: 8px;
                        margin-bottom: 2rem;
                    }
                    .preview-actions {
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        z-index: 1000;
                    }
                    .preview-btn {
                        margin: 0 5px;
                        padding: 8px 16px;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-family: inherit;
                    }
                    .btn-print { background: #007bff; color: white; }
                    .btn-close { background: #6c757d; color: white; }
                </style>
            </head>
            <body>
                <div class="preview-actions">
                    <button class="preview-btn btn-print" onclick="window.print()">
                        طباعة
                    </button>
                    <button class="preview-btn btn-close" onclick="window.close()">
                        إغلاق
                    </button>
                </div>

                <div class="preview-header">
                    <h2>معاينة التقرير قبل الطباعة</h2>
                    <p>هذه معاينة لكيفية ظهور التقرير عند الطباعة</p>
                </div>

                ${reportContent.innerHTML}
            </body>
            </html>
        `;

        previewWindow.document.write(previewHTML);
        previewWindow.document.close();

        showNotification('تم فتح معاينة التقرير في نافذة جديدة', 'success');
    }

    togglePrintMode() {
        const body = document.body;
        const isInPrintMode = body.classList.contains('print-mode');

        if (isInPrintMode) {
            body.classList.remove('print-mode');
            showNotification('تم إلغاء وضع الطباعة', 'info');
        } else {
            body.classList.add('print-mode');
            showNotification('تم تفعيل وضع الطباعة - يمكنك الآن رؤية التقرير كما سيظهر عند الطباعة', 'success');
        }
    }

    printSettings() {
        const settingsModal = `
            <div class="modal-header">
                <h3>إعدادات الطباعة المتقدمة</h3>
                <button class="btn btn-sm btn-danger" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="print-settings-content">
                <div class="setting-group">
                    <h4>إعدادات الصفحة</h4>
                    <div class="setting-item">
                        <label>
                            <input type="radio" name="page-size" value="A4" checked>
                            A4 (21 × 29.7 سم)
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="radio" name="page-size" value="A3">
                            A3 (29.7 × 42 سم)
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="radio" name="orientation" value="portrait" checked>
                            عمودي (Portrait)
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="radio" name="orientation" value="landscape">
                            أفقي (Landscape)
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <h4>إعدادات المحتوى</h4>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked>
                            إظهار الرأس والتذييل
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked>
                            إظهار خانات التوقيع
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked>
                            طباعة الألوان والخلفيات
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox">
                            إضافة رقم الصفحة
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <h4>إعدادات الجودة</h4>
                    <div class="setting-item">
                        <label>جودة الطباعة:</label>
                        <select>
                            <option value="high">عالية (600 DPI)</option>
                            <option value="medium" selected>متوسطة (300 DPI)</option>
                            <option value="low">منخفضة (150 DPI)</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>حجم الخط:</label>
                        <select>
                            <option value="small">صغير</option>
                            <option value="medium" selected>متوسط</option>
                            <option value="large">كبير</option>
                        </select>
                    </div>
                </div>

                <div class="modal-actions">
                    <button class="btn btn-primary" onclick="reportsManager.applyPrintSettings()">
                        <i class="fas fa-check"></i> تطبيق الإعدادات
                    </button>
                    <button class="btn btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </div>
        `;

        showModal(settingsModal);
    }

    applyPrintSettings() {
        // تطبيق إعدادات الطباعة المحددة
        showNotification('تم تطبيق إعدادات الطباعة بنجاح', 'success');
        closeModal();
    }

    exportReportToPDF() {
        // تحسين تصدير PDF
        const reportContent = document.getElementById('report-content');
        if (!reportContent) {
            showNotification('لا يوجد تقرير للتصدير', 'warning');
            return;
        }

        // إضافة فئة PDF للتنسيق الخاص
        document.body.classList.add('pdf-export');

        // استخدام window.print مع إعدادات PDF
        setTimeout(() => {
            window.print();

            setTimeout(() => {
                document.body.classList.remove('pdf-export');
            }, 1000);
        }, 500);

        showNotification('تم إعداد التقرير للتصدير كـ PDF - اختر "حفظ كـ PDF" من خيارات الطباعة', 'info');
    }

    exportReportToWord() {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) {
            showNotification('لا يوجد تقرير للتصدير', 'warning');
            return;
        }

        try {
            // إنشاء محتوى HTML للتصدير إلى Word
            const wordContent = `
                <!DOCTYPE html>
                <html xmlns:o='urn:schemas-microsoft-com:office:office'
                      xmlns:w='urn:schemas-microsoft-com:office:word'
                      xmlns='http://www.w3.org/TR/REC-html40'>
                <head>
                    <meta charset='utf-8'>
                    <title>تقرير تقنية المعلومات</title>
                    <style>
                        body { font-family: 'Arial', sans-serif; direction: rtl; }
                        table { border-collapse: collapse; width: 100%; }
                        th, td { border: 1px solid #000; padding: 8px; text-align: center; }
                        th { background-color: #f0f0f0; font-weight: bold; }
                        .print-header { text-align: center; margin-bottom: 20px; }
                        .signature-section { margin-top: 30px; }
                    </style>
                </head>
                <body>
                    ${reportContent.innerHTML}
                </body>
                </html>
            `;

            // إنشاء Blob وتحميل الملف
            const blob = new Blob([wordContent], {
                type: 'application/msword'
            });

            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `تقرير_تقنية_المعلومات_${new Date().toISOString().split('T')[0]}.doc`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            showNotification('تم تصدير التقرير إلى Word بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير Word:', error);
            showNotification('خطأ في تصدير التقرير إلى Word', 'error');
        }
    }

    exportReportToExcel() {
        const reportContent = document.getElementById('report-content');
        const tables = reportContent.querySelectorAll('table');

        if (tables.length === 0) {
            showNotification('لا توجد جداول للتصدير', 'warning');
            return;
        }

        try {
            const wb = XLSX.utils.book_new();

            tables.forEach((table, index) => {
                const ws = XLSX.utils.table_to_sheet(table);

                // تحسين تنسيق Excel
                const range = XLSX.utils.decode_range(ws['!ref']);

                // تنسيق الرأس
                for (let C = range.s.c; C <= range.e.c; ++C) {
                    const headerCell = XLSX.utils.encode_cell({ r: 0, c: C });
                    if (ws[headerCell]) {
                        ws[headerCell].s = {
                            font: { bold: true, color: { rgb: "FFFFFF" } },
                            fill: { fgColor: { rgb: "2c3e50" } },
                            alignment: { horizontal: "center", vertical: "center" }
                        };
                    }
                }

                // تنسيق البيانات
                for (let R = range.s.r + 1; R <= range.e.r; ++R) {
                    for (let C = range.s.c; C <= range.e.c; ++C) {
                        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
                        if (ws[cellAddress]) {
                            ws[cellAddress].s = {
                                alignment: { horizontal: "center", vertical: "center" },
                                border: {
                                    top: { style: "thin" },
                                    bottom: { style: "thin" },
                                    left: { style: "thin" },
                                    right: { style: "thin" }
                                }
                            };
                        }
                    }
                }

                const sheetName = index === 0 ? 'التقرير الرئيسي' : `جدول_${index + 1}`;
                XLSX.utils.book_append_sheet(wb, ws, sheetName);
            });

            const fileName = `تقرير_تقنية_المعلومات_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);

            showNotification('تم تصدير التقرير إلى Excel بنجاح مع التنسيق المحسن', 'success');

        } catch (error) {
            console.error('خطأ في تصدير Excel:', error);
            showNotification('خطأ في تصدير التقرير إلى Excel', 'error');
        }
    }
}

// إنشاء مثيل من مدير التقارير
const reportsManager = new ReportsManager();

// دوال مساعدة
function generateSemesterReport() {
    reportsManager.generateSemesterReport();
}

function generateAnnualReport() {
    reportsManager.generateAnnualReport();
}

function showStatistics() {
    reportsManager.showStatistics();
}

function showCharts() {
    reportsManager.showCharts();
}
