<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طرق استيراد أسماء الطلاب</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .test-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .method-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .method-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .method-title {
            color: #2c3e50;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .method-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        .method-example {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #219a52);
        }
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        .advantages {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 0 8px 8px 0;
        }
        .advantages h4 {
            color: #27ae60;
            margin-bottom: 8px;
        }
        .advantages ul {
            margin: 0;
            padding-right: 20px;
        }
        .advantages li {
            color: #2d5a2d;
            margin-bottom: 5px;
        }
        .download-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .download-section h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .file-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .file-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .file-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        .file-icon.txt { color: #6c757d; }
        .file-icon.csv { color: #28a745; }
        .file-icon.excel { color: #198754; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-file-import"></i> طرق استيراد أسماء الطلاب</h1>
            <p>حلول بسيطة وعملية لرفع أسماء الطلاب في تطبيق سطح المكتب</p>
        </div>

        <!-- الطريقة الأولى: ملف نصي -->
        <div class="method-card">
            <div class="method-title">
                <i class="fas fa-file-text"></i>
                الطريقة الأولى: استيراد من ملف نصي (.txt)
            </div>
            <div class="method-description">
                الطريقة الأبسط والأسهل - أنشئ ملف نصي واكتب اسم كل طالب في سطر منفصل
            </div>
            
            <div class="method-example">مثال على محتوى الملف النصي:
أحمد محمد علي
فاطمة أحمد سالم
محمد علي حسن
نور الدين خالد
سارة عبدالله محمد
علي أحمد سالم
مريم محمد علي</div>

            <div class="advantages">
                <h4>المميزات:</h4>
                <ul>
                    <li>أبسط طريقة على الإطلاق</li>
                    <li>يمكن إنشاؤها بأي محرر نصوص</li>
                    <li>لا تحتاج برامج خاصة</li>
                    <li>سريعة في التحميل</li>
                </ul>
            </div>

            <button class="btn btn-success" onclick="createSampleTextFile()">
                <i class="fas fa-download"></i> تحميل ملف نموذجي
            </button>
        </div>

        <!-- الطريقة الثانية: ملف CSV -->
        <div class="method-card">
            <div class="method-title">
                <i class="fas fa-file-csv"></i>
                الطريقة الثانية: استيراد من ملف CSV
            </div>
            <div class="method-description">
                ملف CSV يحتوي على رقم الطالب واسمه، أو الاسم فقط
            </div>
            
            <div class="method-example">مثال على محتوى ملف CSV:
رقم الطالب,اسم الطالب
أ001,أحمد محمد علي
أ002,فاطمة أحمد سالم
أ003,محمد علي حسن
أ004,نور الدين خالد
أ005,سارة عبدالله محمد</div>

            <div class="advantages">
                <h4>المميزات:</h4>
                <ul>
                    <li>يدعم رقم الطالب والاسم معاً</li>
                    <li>يمكن إنشاؤه من Excel</li>
                    <li>تنسيق منظم ومرتب</li>
                    <li>سهل التحرير</li>
                </ul>
            </div>

            <button class="btn btn-success" onclick="createSampleCSVFile()">
                <i class="fas fa-download"></i> تحميل ملف نموذجي
            </button>
        </div>

        <!-- الطريقة الثالثة: ملف Excel -->
        <div class="method-card">
            <div class="method-title">
                <i class="fas fa-file-excel"></i>
                الطريقة الثالثة: استيراد من Excel
            </div>
            <div class="method-description">
                استيراد مباشر من ملفات Excel (.xlsx أو .xls)
            </div>
            
            <div class="method-example">تنسيق ملف Excel:
العمود A: رقم الطالب (اختياري)
العمود B: اسم الطالب

أو العمود A فقط: اسم الطالب</div>

            <div class="advantages">
                <h4>المميزات:</h4>
                <ul>
                    <li>التنسيق المألوف للجميع</li>
                    <li>يدعم التنسيق المتقدم</li>
                    <li>يمكن نسخه من قوائم موجودة</li>
                    <li>دعم كامل للأرقام والنصوص</li>
                </ul>
            </div>

            <button class="btn btn-success" onclick="createSampleExcelFile()">
                <i class="fas fa-download"></i> تحميل ملف نموذجي
            </button>
        </div>

        <!-- الطريقة الرابعة: الإدخال السريع -->
        <div class="method-card">
            <div class="method-title">
                <i class="fas fa-keyboard"></i>
                الطريقة الرابعة: الإدخال السريع
            </div>
            <div class="method-description">
                نافذة إدخال سريع داخل التطبيق لكتابة الأسماء مباشرة
            </div>
            
            <div class="method-example">طريقة الاستخدام:
1. اضغط زر "إدخال سريع"
2. اكتب اسم كل طالب في سطر منفصل
3. اضغط "إضافة الأسماء"</div>

            <div class="advantages">
                <h4>المميزات:</h4>
                <ul>
                    <li>لا تحتاج ملفات خارجية</li>
                    <li>سريعة للأعداد الصغيرة</li>
                    <li>تعديل فوري</li>
                    <li>واجهة بسيطة وواضحة</li>
                </ul>
            </div>

            <button class="btn btn-info" onclick="showQuickEntryDemo()">
                <i class="fas fa-eye"></i> عرض توضيحي
            </button>
        </div>

        <!-- قسم تحميل الملفات النموذجية -->
        <div class="download-section">
            <h3><i class="fas fa-download"></i> تحميل ملفات نموذجية</h3>
            <p>حمل هذه الملفات النموذجية لتجربة طرق الاستيراد المختلفة:</p>
            
            <div class="file-grid">
                <div class="file-card">
                    <div class="file-icon txt"><i class="fas fa-file-text"></i></div>
                    <h4>ملف نصي</h4>
                    <p>أسماء الطلاب في ملف .txt</p>
                    <button class="btn" onclick="createSampleTextFile()">تحميل</button>
                </div>
                
                <div class="file-card">
                    <div class="file-icon csv"><i class="fas fa-file-csv"></i></div>
                    <h4>ملف CSV</h4>
                    <p>بيانات منظمة في ملف .csv</p>
                    <button class="btn" onclick="createSampleCSVFile()">تحميل</button>
                </div>
                
                <div class="file-card">
                    <div class="file-icon excel"><i class="fas fa-file-excel"></i></div>
                    <h4>ملف Excel</h4>
                    <p>جدول Excel جاهز للاستخدام</p>
                    <button class="btn" onclick="createSampleExcelFile()">تحميل</button>
                </div>
            </div>
        </div>

        <!-- زر فتح التطبيق -->
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn btn-warning" onclick="openMainApp()" style="font-size: 16px; padding: 15px 30px;">
                <i class="fas fa-external-link-alt"></i> فتح التطبيق لتجربة الاستيراد
            </button>
        </div>
    </div>

    <script>
        function createSampleTextFile() {
            const content = `أحمد محمد علي
فاطمة أحمد سالم
محمد علي حسن
نور الدين خالد
سارة عبدالله محمد
علي أحمد سالم
مريم محمد علي
يوسف عبدالرحمن
زينب محمد أحمد
عبدالله علي محمد`;

            downloadFile(content, 'اسماء_الطلاب.txt', 'text/plain');
        }

        function createSampleCSVFile() {
            const content = `رقم الطالب,اسم الطالب
أ001,أحمد محمد علي
أ002,فاطمة أحمد سالم
أ003,محمد علي حسن
أ004,نور الدين خالد
أ005,سارة عبدالله محمد
أ006,علي أحمد سالم
أ007,مريم محمد علي
أ008,يوسف عبدالرحمن
أ009,زينب محمد أحمد
أ010,عبدالله علي محمد`;

            downloadFile(content, 'بيانات_الطلاب.csv', 'text/csv');
        }

        function createSampleExcelFile() {
            // إنشاء ملف Excel بسيط باستخدام HTML table
            const content = `
                <table>
                    <tr><th>رقم الطالب</th><th>اسم الطالب</th></tr>
                    <tr><td>أ001</td><td>أحمد محمد علي</td></tr>
                    <tr><td>أ002</td><td>فاطمة أحمد سالم</td></tr>
                    <tr><td>أ003</td><td>محمد علي حسن</td></tr>
                    <tr><td>أ004</td><td>نور الدين خالد</td></tr>
                    <tr><td>أ005</td><td>سارة عبدالله محمد</td></tr>
                    <tr><td>أ006</td><td>علي أحمد سالم</td></tr>
                    <tr><td>أ007</td><td>مريم محمد علي</td></tr>
                    <tr><td>أ008</td><td>يوسف عبدالرحمن</td></tr>
                    <tr><td>أ009</td><td>زينب محمد أحمد</td></tr>
                    <tr><td>أ010</td><td>عبدالله علي محمد</td></tr>
                </table>
            `;
            
            downloadFile(content, 'بيانات_الطلاب.xls', 'application/vnd.ms-excel');
        }

        function showQuickEntryDemo() {
            alert('الإدخال السريع:\n\n1. افتح التطبيق الرئيسي\n2. انتقل إلى "رفع درجات الطلاب"\n3. اختر الصف والشعبة\n4. اضغط "إدخال سريع"\n5. اكتب الأسماء كل اسم في سطر\n6. اضغط "إضافة الأسماء"');
        }

        function openMainApp() {
            window.open('index.html', '_blank');
        }

        function downloadFile(content, filename, contentType = 'text/plain') {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
