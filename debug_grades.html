<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة تحميل الدرجات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .debug-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .test-form {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }
        select, input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>تشخيص مشكلة تحميل الدرجات</h1>
        
        <div class="debug-section">
            <h3>1. فحص قاعدة البيانات</h3>
            <button onclick="checkDatabase()">فحص قاعدة البيانات</button>
            <div id="db-result" class="debug-result"></div>
        </div>

        <div class="debug-section">
            <h3>2. فحص الطلاب</h3>
            <button onclick="checkStudents()">عرض جميع الطلاب</button>
            <div id="students-result" class="debug-result"></div>
        </div>

        <div class="debug-section">
            <h3>3. فحص الطلاب بفلتر</h3>
            <div class="test-form">
                <label>الصف:</label>
                <select id="test-grade">
                    <option value="">اختر الصف</option>
                    <option value="1">الصف الأول</option>
                    <option value="2">الصف الثاني</option>
                    <option value="3">الصف الثالث</option>
                    <option value="4">الصف الرابع</option>
                    <option value="5">الصف الخامس</option>
                    <option value="6">الصف السادس</option>
                    <option value="7">الصف السابع</option>
                    <option value="8">الصف الثامن</option>
                    <option value="9">الصف التاسع</option>
                    <option value="10">الصف العاشر</option>
                    <option value="11">الصف الحادي عشر</option>
                    <option value="12">الصف الثاني عشر</option>
                </select>
                <label>الشعبة:</label>
                <select id="test-section">
                    <option value="">اختر الشعبة</option>
                    <option value="أ">أ</option>
                    <option value="ب">ب</option>
                    <option value="ج">ج</option>
                    <option value="د">د</option>
                </select>
                <button onclick="checkStudentsWithFilter()">فحص</button>
            </div>
            <div id="filtered-students-result" class="debug-result"></div>
        </div>

        <div class="debug-section">
            <h3>4. محاكاة تحميل جدول الدرجات</h3>
            <div class="test-form">
                <label>العام الدراسي:</label>
                <select id="test-academic-year">
                    <option value="2024-2025">2024-2025</option>
                    <option value="2023-2024">2023-2024</option>
                </select>
                <label>الفصل:</label>
                <select id="test-semester">
                    <option value="1">الفصل الأول</option>
                    <option value="2">الفصل الثاني</option>
                </select>
                <button onclick="simulateLoadGradeSheet()">محاكاة التحميل</button>
            </div>
            <div id="simulation-result" class="debug-result"></div>
        </div>

        <div class="debug-section">
            <h3>5. إضافة طالب تجريبي</h3>
            <div class="test-form">
                <input type="text" id="test-student-name" placeholder="اسم الطالب" value="طالب تجريبي">
                <input type="number" id="test-student-number" placeholder="رقم الطالب" value="1001">
                <select id="test-student-grade">
                    <option value="1">الصف الأول</option>
                    <option value="2">الصف الثاني</option>
                    <option value="3">الصف الثالث</option>
                </select>
                <select id="test-student-section">
                    <option value="أ">أ</option>
                    <option value="ب">ب</option>
                </select>
                <button onclick="addTestStudent()">إضافة طالب</button>
            </div>
            <div id="add-student-result" class="debug-result"></div>
        </div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="js/database.js"></script>
    <script>
        // انتظار تحميل قاعدة البيانات
        let dbManager;
        
        window.addEventListener('DOMContentLoaded', async () => {
            try {
                dbManager = new DatabaseManager();
                await new Promise(resolve => setTimeout(resolve, 2000)); // انتظار تحميل قاعدة البيانات
                document.getElementById('db-result').textContent = '✓ قاعدة البيانات جاهزة';
                document.getElementById('db-result').className = 'debug-result success';
            } catch (error) {
                document.getElementById('db-result').textContent = '✗ خطأ في تحميل قاعدة البيانات: ' + error.message;
                document.getElementById('db-result').className = 'debug-result error';
            }
        });

        async function checkDatabase() {
            const result = document.getElementById('db-result');
            try {
                if (!dbManager || !dbManager.db) {
                    result.textContent = '✗ قاعدة البيانات غير متاحة';
                    result.className = 'debug-result error';
                    return;
                }
                
                result.textContent = '✓ قاعدة البيانات متاحة ومتصلة';
                result.className = 'debug-result success';
            } catch (error) {
                result.textContent = '✗ خطأ: ' + error.message;
                result.className = 'debug-result error';
            }
        }

        async function checkStudents() {
            const result = document.getElementById('students-result');
            try {
                const students = await dbManager.getStudents();
                result.textContent = `عدد الطلاب: ${students.length}\n\nالطلاب:\n${JSON.stringify(students, null, 2)}`;
                result.className = 'debug-result ' + (students.length > 0 ? 'success' : 'warning');
            } catch (error) {
                result.textContent = '✗ خطأ في تحميل الطلاب: ' + error.message;
                result.className = 'debug-result error';
            }
        }

        async function checkStudentsWithFilter() {
            const result = document.getElementById('filtered-students-result');
            const grade = document.getElementById('test-grade').value;
            const section = document.getElementById('test-section').value;
            
            try {
                const filters = {};
                if (grade) filters.grade = grade;
                if (section) filters.section = section;
                
                const students = await dbManager.getStudents(filters);
                result.textContent = `الفلتر: الصف ${grade || 'الكل'}, الشعبة ${section || 'الكل'}\nعدد الطلاب: ${students.length}\n\nالطلاب:\n${JSON.stringify(students, null, 2)}`;
                result.className = 'debug-result ' + (students.length > 0 ? 'success' : 'warning');
            } catch (error) {
                result.textContent = '✗ خطأ في تحميل الطلاب المفلترين: ' + error.message;
                result.className = 'debug-result error';
            }
        }

        async function simulateLoadGradeSheet() {
            const result = document.getElementById('simulation-result');
            const academicYear = document.getElementById('test-academic-year').value;
            const semester = document.getElementById('test-semester').value;
            const grade = document.getElementById('test-grade').value;
            const section = document.getElementById('test-section').value;
            
            try {
                if (!academicYear || !semester || !grade || !section) {
                    result.textContent = '⚠ يرجى اختيار جميع الحقول المطلوبة';
                    result.className = 'debug-result warning';
                    return;
                }

                // محاكاة نفس منطق loadGradeSheet
                const students = await dbManager.getStudents({ grade: grade, section: section });
                
                if (students.length === 0) {
                    result.textContent = '⚠ لا توجد طلاب في هذا الصف والشعبة';
                    result.className = 'debug-result warning';
                    return;
                }

                const existingGrades = await dbManager.getGrades({
                    academic_year: academicYear,
                    semester: semester,
                    grade_level: grade
                });

                result.textContent = `✓ نجح التحميل!\nعدد الطلاب: ${students.length}\nعدد الدرجات الموجودة: ${existingGrades.length}\n\nالطلاب:\n${students.map(s => `${s.student_number} - ${s.name}`).join('\n')}`;
                result.className = 'debug-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في محاكاة التحميل: ' + error.message;
                result.className = 'debug-result error';
            }
        }

        async function addTestStudent() {
            const result = document.getElementById('add-student-result');
            const name = document.getElementById('test-student-name').value;
            const number = document.getElementById('test-student-number').value;
            const grade = document.getElementById('test-student-grade').value;
            const section = document.getElementById('test-student-section').value;
            
            try {
                const studentData = {
                    student_number: number,
                    name: name,
                    grade: grade,
                    section: section
                };
                
                await dbManager.addStudent(studentData);
                result.textContent = '✓ تم إضافة الطالب بنجاح';
                result.className = 'debug-result success';
                
                // تحديث عدد الطلاب
                setTimeout(checkStudents, 500);
                
            } catch (error) {
                result.textContent = '✗ خطأ في إضافة الطالب: ' + error.message;
                result.className = 'debug-result error';
            }
        }
    </script>
</body>
</html>
