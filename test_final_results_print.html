<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طباعة النتائج النهائية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .test-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #219a52;
        }
        .btn-warning {
            background: #f39c12;
        }
        .btn-warning:hover {
            background: #e67e22;
        }
        .test-form {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }
        select, input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار طباعة النتائج النهائية</h1>
        
        <div class="test-section">
            <h3>1. إعداد البيانات التجريبية</h3>
            <button onclick="setupTestData()">إعداد بيانات تجريبية</button>
            <div id="setup-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>2. محاكاة تحميل النتائج النهائية</h3>
            <div class="test-form">
                <label>العام الدراسي:</label>
                <select id="test-academic-year">
                    <option value="2024-2025">2024-2025</option>
                </select>
                <label>الصف:</label>
                <select id="test-grade">
                    <option value="1">الصف الأول</option>
                    <option value="2">الصف الثاني</option>
                </select>
                <label>الشعبة:</label>
                <select id="test-section">
                    <option value="أ">أ</option>
                    <option value="ب">ب</option>
                </select>
                <button onclick="loadTestResults()">تحميل النتائج</button>
            </div>
            <div id="load-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار دوال الطباعة</h3>
            <button onclick="testPrintContent()">اختبار محتوى الطباعة</button>
            <button class="btn-success" onclick="testPrintWindow()">اختبار نافذة الطباعة</button>
            <button class="btn-warning" onclick="testPrintPreview()">معاينة الطباعة</button>
            <div id="print-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار أنواع الطباعة المختلفة</h3>
            <button onclick="testPrintType('color')">طباعة ملونة</button>
            <button onclick="testPrintType('bw')">أبيض وأسود</button>
            <button onclick="testPrintType('official')">طباعة رسمية</button>
            <div id="print-types-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>5. فتح التطبيق الرئيسي</h3>
            <button class="btn-success" onclick="openMainApp()">فتح التطبيق</button>
            <div id="app-result" class="test-result"></div>
        </div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="js/database.js"></script>
    <script src="js/final-results.js"></script>
    <script>
        let dbManager;
        let finalResultsManager;
        
        window.addEventListener('DOMContentLoaded', async () => {
            try {
                dbManager = new DatabaseManager();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                finalResultsManager = new FinalResultsManager();
                
                document.getElementById('setup-result').textContent = '✓ تم تحميل النظام بنجاح';
                document.getElementById('setup-result').className = 'test-result success';
            } catch (error) {
                document.getElementById('setup-result').textContent = '✗ خطأ في تحميل النظام: ' + error.message;
                document.getElementById('setup-result').className = 'test-result error';
            }
        });

        async function setupTestData() {
            const result = document.getElementById('setup-result');
            try {
                // إضافة طلاب تجريبيين
                const students = [
                    { student_number: '1001', name: 'أحمد محمد علي', grade: '1', section: 'أ' },
                    { student_number: '1002', name: 'فاطمة أحمد سالم', grade: '1', section: 'أ' },
                    { student_number: '1003', name: 'محمد علي حسن', grade: '1', section: 'أ' },
                ];

                for (const student of students) {
                    try {
                        await dbManager.addStudent(student);
                    } catch (error) {
                        // تجاهل خطأ الطالب المكرر
                    }
                }

                // إضافة درجات تجريبية
                const grades = [
                    { student_id: 1, academic_year: '2024-2025', semester: 1, grade_level: '1', total: 85 },
                    { student_id: 1, academic_year: '2024-2025', semester: 2, grade_level: '1', total: 90 },
                    { student_id: 2, academic_year: '2024-2025', semester: 1, grade_level: '1', total: 78 },
                    { student_id: 2, academic_year: '2024-2025', semester: 2, grade_level: '1', total: 82 },
                    { student_id: 3, academic_year: '2024-2025', semester: 1, grade_level: '1', total: 92 },
                    { student_id: 3, academic_year: '2024-2025', semester: 2, grade_level: '1', total: 88 },
                ];

                for (const grade of grades) {
                    try {
                        await dbManager.saveGrades(grade);
                    } catch (error) {
                        // تجاهل الأخطاء
                    }
                }

                result.textContent = '✓ تم إعداد البيانات التجريبية بنجاح';
                result.className = 'test-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في إعداد البيانات: ' + error.message;
                result.className = 'test-result error';
            }
        }

        async function loadTestResults() {
            const result = document.getElementById('load-result');
            try {
                const academicYear = document.getElementById('test-academic-year').value;
                const grade = document.getElementById('test-grade').value;
                const section = document.getElementById('test-section').value;

                // محاكاة تحميل النتائج
                const students = await dbManager.getStudents({ grade: grade, section: section });
                
                if (students.length === 0) {
                    result.textContent = '⚠ لا توجد طلاب. يرجى إعداد البيانات التجريبية أولاً';
                    result.className = 'test-result warning';
                    return;
                }

                const semester1Grades = await dbManager.getGrades({
                    academic_year: academicYear,
                    semester: 1,
                    grade_level: grade
                });

                const semester2Grades = await dbManager.getGrades({
                    academic_year: academicYear,
                    semester: 2,
                    grade_level: grade
                });

                // حساب النتائج النهائية
                const finalResults = finalResultsManager.calculateFinalResults(students, semester1Grades, semester2Grades);
                
                result.textContent = `✓ تم تحميل النتائج بنجاح!\nعدد الطلاب: ${students.length}\nعدد النتائج: ${finalResults.length}\nدرجات الفصل الأول: ${semester1Grades.length}\nدرجات الفصل الثاني: ${semester2Grades.length}`;
                result.className = 'test-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في تحميل النتائج: ' + error.message;
                result.className = 'test-result error';
            }
        }

        function testPrintContent() {
            const result = document.getElementById('print-result');
            try {
                if (!finalResultsManager.currentResults || finalResultsManager.currentResults.length === 0) {
                    result.textContent = '⚠ لا توجد نتائج محملة. يرجى تحميل النتائج أولاً';
                    result.className = 'test-result warning';
                    return;
                }

                const printContent = finalResultsManager.generateFinalResultsPrintContent('color');
                
                if (printContent.includes('لا توجد نتائج')) {
                    result.textContent = '✗ محتوى الطباعة فارغ';
                    result.className = 'test-result error';
                } else {
                    result.textContent = `✓ تم إنشاء محتوى الطباعة بنجاح\nطول المحتوى: ${printContent.length} حرف\nيحتوي على جدول: ${printContent.includes('<table>') ? 'نعم' : 'لا'}`;
                    result.className = 'test-result success';
                }
                
            } catch (error) {
                result.textContent = '✗ خطأ في اختبار محتوى الطباعة: ' + error.message;
                result.className = 'test-result error';
            }
        }

        function testPrintWindow() {
            const result = document.getElementById('print-result');
            try {
                if (!finalResultsManager.currentResults || finalResultsManager.currentResults.length === 0) {
                    result.textContent = '⚠ لا توجد نتائج محملة. يرجى تحميل النتائج أولاً';
                    result.className = 'test-result warning';
                    return;
                }

                // اختبار فتح نافذة الطباعة
                finalResultsManager.printResults('color');
                
                result.textContent = '✓ تم فتح نافذة الطباعة. تحقق من النافذة الجديدة';
                result.className = 'test-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في فتح نافذة الطباعة: ' + error.message;
                result.className = 'test-result error';
            }
        }

        function testPrintPreview() {
            const result = document.getElementById('print-result');
            try {
                if (!finalResultsManager.currentResults || finalResultsManager.currentResults.length === 0) {
                    result.textContent = '⚠ لا توجد نتائج محملة. يرجى تحميل النتائج أولاً';
                    result.className = 'test-result warning';
                    return;
                }

                const printContent = finalResultsManager.generateFinalResultsPrintContent('color');
                const previewWindow = window.open('', '_blank');
                previewWindow.document.write(printContent);
                previewWindow.document.close();
                
                result.textContent = '✓ تم فتح معاينة الطباعة في نافذة جديدة';
                result.className = 'test-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في معاينة الطباعة: ' + error.message;
                result.className = 'test-result error';
            }
        }

        function testPrintType(type) {
            const result = document.getElementById('print-types-result');
            try {
                if (!finalResultsManager.currentResults || finalResultsManager.currentResults.length === 0) {
                    result.textContent = '⚠ لا توجد نتائج محملة. يرجى تحميل النتائج أولاً';
                    result.className = 'test-result warning';
                    return;
                }

                const printContent = finalResultsManager.generateFinalResultsPrintContent(type);
                const testWindow = window.open('', '_blank');
                testWindow.document.write(printContent);
                testWindow.document.close();
                
                result.textContent = `✓ تم اختبار الطباعة ${finalResultsManager.getPrintTypeName(type)} بنجاح`;
                result.className = 'test-result success';
                
            } catch (error) {
                result.textContent = `✗ خطأ في اختبار الطباعة ${type}: ` + error.message;
                result.className = 'test-result error';
            }
        }

        function openMainApp() {
            const result = document.getElementById('app-result');
            try {
                window.open('index.html', '_blank');
                result.textContent = '✓ تم فتح التطبيق الرئيسي في نافذة جديدة';
                result.className = 'test-result success';
            } catch (error) {
                result.textContent = '✗ خطأ في فتح التطبيق: ' + error.message;
                result.className = 'test-result error';
            }
        }
    </script>
</body>
</html>
