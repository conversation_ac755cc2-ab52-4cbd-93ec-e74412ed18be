// قاعدة البيانات SQLite للتطبيق
class DatabaseManager {
    constructor() {
        this.dbName = 'it_evaluation_system';
        this.version = 1;
        this.db = null;
        this.init();
    }

    async init() {
        try {
            // استخدام IndexedDB كبديل لـ SQLite في المتصفح
            this.db = await this.openDatabase();
            await this.createTables();
            console.log('تم تهيئة قاعدة البيانات بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة قاعدة البيانات:', error);
        }
    }

    openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                this.createObjectStores(db);
            };
        });
    }

    createObjectStores(db) {
        // جدول الطلاب
        if (!db.objectStoreNames.contains('students')) {
            const studentsStore = db.createObjectStore('students', { keyPath: 'id', autoIncrement: true });
            studentsStore.createIndex('student_number', 'student_number', { unique: true });
            studentsStore.createIndex('grade', 'grade', { unique: false });
            studentsStore.createIndex('section', 'section', { unique: false });
            studentsStore.createIndex('name', 'name', { unique: false });
        }

        // جدول الدرجات
        if (!db.objectStoreNames.contains('grades')) {
            const gradesStore = db.createObjectStore('grades', { keyPath: 'id', autoIncrement: true });
            gradesStore.createIndex('student_id', 'student_id', { unique: false });
            gradesStore.createIndex('academic_year', 'academic_year', { unique: false });
            gradesStore.createIndex('semester', 'semester', { unique: false });
            gradesStore.createIndex('grade_level', 'grade_level', { unique: false });
        }

        // جدول الإعدادات
        if (!db.objectStoreNames.contains('settings')) {
            const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
        }

        // جدول السنوات الدراسية
        if (!db.objectStoreNames.contains('academic_years')) {
            const yearsStore = db.createObjectStore('academic_years', { keyPath: 'id', autoIncrement: true });
            yearsStore.createIndex('year', 'year', { unique: true });
        }
    }

    async createTables() {
        // إدراج البيانات الافتراضية
        await this.insertDefaultData();
    }

    async insertDefaultData() {
        try {
            // إدراج السنة الدراسية الحالية
            await this.addAcademicYear('2024-2025', true);

            // إدراج الإعدادات الافتراضية
            await this.setSetting('school_name', 'مدرسة تقنية المعلومات');
            await this.setSetting('current_academic_year', '2024-2025');
            await this.setSetting('ministry_name', 'وزارة التربية والتعليم');
            await this.setSetting('directorate_name', 'مديرية التربية والتعليم');
            await this.setSetting('principal_name', '');
            await this.setSetting('supervisor_name', '');
            await this.setSetting('teacher_name', '');
            await this.setSetting('app_version', '1.0.0');

        } catch (error) {
            console.log('البيانات الافتراضية موجودة مسبقاً');
        }
    }

    // عمليات الطلاب
    async addStudent(studentData) {
        const transaction = this.db.transaction(['students'], 'readwrite');
        const store = transaction.objectStore('students');
        
        const student = {
            student_number: studentData.student_number,
            name: studentData.name,
            grade: parseInt(studentData.grade),
            section: studentData.section,
            registration_date: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        return new Promise((resolve, reject) => {
            const request = store.add(student);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getStudents(filters = {}) {
        const transaction = this.db.transaction(['students'], 'readonly');
        const store = transaction.objectStore('students');

        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => {
                let students = request.result;
                
                // تطبيق الفلاتر
                if (filters.grade) {
                    students = students.filter(s => s.grade == filters.grade);
                }
                if (filters.section) {
                    students = students.filter(s => s.section === filters.section);
                }
                if (filters.search) {
                    const searchTerm = filters.search.toLowerCase();
                    students = students.filter(s => 
                        s.name.toLowerCase().includes(searchTerm) ||
                        s.student_number.toString().includes(searchTerm)
                    );
                }
                
                resolve(students);
            };
            request.onerror = () => reject(request.error);
        });
    }

    async updateStudent(id, studentData) {
        const transaction = this.db.transaction(['students'], 'readwrite');
        const store = transaction.objectStore('students');

        return new Promise((resolve, reject) => {
            const getRequest = store.get(id);
            getRequest.onsuccess = () => {
                const student = getRequest.result;
                if (student) {
                    Object.assign(student, studentData);
                    student.updated_at = new Date().toISOString();
                    
                    const updateRequest = store.put(student);
                    updateRequest.onsuccess = () => resolve(updateRequest.result);
                    updateRequest.onerror = () => reject(updateRequest.error);
                } else {
                    reject(new Error('الطالب غير موجود'));
                }
            };
            getRequest.onerror = () => reject(getRequest.error);
        });
    }

    async deleteStudent(id) {
        const transaction = this.db.transaction(['students'], 'readwrite');
        const store = transaction.objectStore('students');

        return new Promise((resolve, reject) => {
            const request = store.delete(id);
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    // عمليات الدرجات
    async saveGrades(gradesData) {
        const transaction = this.db.transaction(['grades'], 'readwrite');
        const store = transaction.objectStore('grades');

        const gradeRecord = {
            student_id: gradesData.student_id,
            academic_year: gradesData.academic_year,
            semester: gradesData.semester,
            grade_level: gradesData.grade_level,
            oral_work_1: gradesData.oral_work_1 || 0,
            oral_work_2: gradesData.oral_work_2 || 0,
            practical_activities_1: gradesData.practical_activities_1 || 0,
            practical_activities_2: gradesData.practical_activities_2 || 0,
            project: gradesData.project || 0,
            short_test: gradesData.short_test || 0,
            final_exam: gradesData.final_exam || 0,
            total: gradesData.total || 0,
            level: gradesData.level || '',
            descriptive_phrase: gradesData.descriptive_phrase || '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        return new Promise((resolve, reject) => {
            const request = store.add(gradeRecord);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getGrades(filters = {}) {
        const transaction = this.db.transaction(['grades'], 'readonly');
        const store = transaction.objectStore('grades');

        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => {
                let grades = request.result;
                
                // تطبيق الفلاتر
                if (filters.student_id) {
                    grades = grades.filter(g => g.student_id == filters.student_id);
                }
                if (filters.academic_year) {
                    grades = grades.filter(g => g.academic_year === filters.academic_year);
                }
                if (filters.semester) {
                    grades = grades.filter(g => g.semester == filters.semester);
                }
                if (filters.grade_level) {
                    grades = grades.filter(g => g.grade_level == filters.grade_level);
                }
                
                resolve(grades);
            };
            request.onerror = () => reject(request.error);
        });
    }

    // عمليات السنوات الدراسية
    async addAcademicYear(year, isCurrent = false) {
        const transaction = this.db.transaction(['academic_years'], 'readwrite');
        const store = transaction.objectStore('academic_years');

        const yearData = {
            year: year,
            is_current: isCurrent,
            created_at: new Date().toISOString()
        };

        return new Promise((resolve, reject) => {
            const request = store.add(yearData);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAcademicYears() {
        const transaction = this.db.transaction(['academic_years'], 'readonly');
        const store = transaction.objectStore('academic_years');

        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // عمليات الإعدادات
    async setSetting(key, value) {
        const transaction = this.db.transaction(['settings'], 'readwrite');
        const store = transaction.objectStore('settings');

        const setting = {
            key: key,
            value: value,
            updated_at: new Date().toISOString()
        };

        return new Promise((resolve, reject) => {
            const request = store.put(setting);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getSetting(key) {
        const transaction = this.db.transaction(['settings'], 'readonly');
        const store = transaction.objectStore('settings');

        return new Promise((resolve, reject) => {
            const request = store.get(key);
            request.onsuccess = () => {
                const result = request.result;
                resolve(result ? result.value : null);
            };
            request.onerror = () => reject(request.error);
        });
    }

    // عمليات النسخ الاحتياطي
    async exportData() {
        const data = {
            students: await this.getStudents(),
            grades: await this.getGrades(),
            academic_years: await this.getAcademicYears(),
            settings: await this.getAllSettings(),
            export_date: new Date().toISOString()
        };
        return data;
    }

    async getAllSettings() {
        const transaction = this.db.transaction(['settings'], 'readonly');
        const store = transaction.objectStore('settings');

        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async importData(data) {
        try {
            // استيراد الطلاب
            if (data.students) {
                for (const student of data.students) {
                    delete student.id; // إزالة المعرف للسماح بإنشاء معرف جديد
                    await this.addStudent(student);
                }
            }

            // استيراد الدرجات
            if (data.grades) {
                for (const grade of data.grades) {
                    delete grade.id;
                    await this.saveGrades(grade);
                }
            }

            // استيراد الإعدادات
            if (data.settings) {
                for (const setting of data.settings) {
                    await this.setSetting(setting.key, setting.value);
                }
            }

            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            throw error;
        }
    }

    // حساب الإحصائيات
    async getStatistics(filters = {}) {
        const grades = await this.getGrades(filters);
        const students = await this.getStudents(filters);

        const stats = {
            total_students: students.length,
            total_grades: grades.length,
            grade_distribution: {
                excellent: 0,
                very_good: 0,
                good: 0,
                acceptable: 0,
                weak: 0
            },
            average_score: 0,
            pass_rate: 0
        };

        if (grades.length > 0) {
            let totalScore = 0;
            let passCount = 0;

            grades.forEach(grade => {
                totalScore += grade.total;
                
                // تحديد التقدير حسب المرحلة
                const gradeLevel = grade.grade_level;
                let gradeCategory = '';
                
                if (gradeLevel >= 1 && gradeLevel <= 4) {
                    // للصفوف 1-4 (من 50)
                    if (grade.total >= 47.25) gradeCategory = 'excellent';
                    else if (grade.total >= 44.75) gradeCategory = 'very_good';
                    else if (grade.total >= 39.75) gradeCategory = 'good';
                    else if (grade.total >= 34.75) gradeCategory = 'acceptable';
                    else gradeCategory = 'weak';
                    
                    if (grade.total >= 25) passCount++;
                } else {
                    // للصفوف 5-12 (من 100)
                    if (grade.total >= 90) gradeCategory = 'excellent';
                    else if (grade.total >= 80) gradeCategory = 'very_good';
                    else if (grade.total >= 70) gradeCategory = 'good';
                    else if (grade.total >= 60) gradeCategory = 'acceptable';
                    else gradeCategory = 'weak';
                    
                    if (grade.total >= 50) passCount++;
                }
                
                stats.grade_distribution[gradeCategory]++;
            });

            stats.average_score = totalScore / grades.length;
            stats.pass_rate = (passCount / grades.length) * 100;
        }

        return stats;
    }

    // الحصول على النتائج النهائية
    async getFinalResults(filters = {}) {
        try {
            // الحصول على الطلاب
            const students = await this.getStudents(filters);

            // الحصول على درجات الفصل الأول والثاني
            const semester1Grades = await this.getGrades({
                ...filters,
                semester: 1
            });

            const semester2Grades = await this.getGrades({
                ...filters,
                semester: 2
            });

            // دمج البيانات
            const finalResults = students.map(student => {
                const s1Grade = semester1Grades.find(g => g.student_id === student.id);
                const s2Grade = semester2Grades.find(g => g.student_id === student.id);

                return {
                    student: student,
                    semester1: s1Grade || null,
                    semester2: s2Grade || null,
                    semester1Total: s1Grade ? s1Grade.total : 0,
                    semester2Total: s2Grade ? s2Grade.total : 0,
                    finalTotal: (s1Grade ? s1Grade.total : 0) + (s2Grade ? s2Grade.total : 0),
                    hasBothSemesters: s1Grade && s2Grade
                };
            });

            return finalResults;

        } catch (error) {
            console.error('خطأ في الحصول على النتائج النهائية:', error);
            throw error;
        }
    }

    // الحصول على إحصائيات النتائج النهائية
    async getFinalResultsStatistics(filters = {}) {
        try {
            const finalResults = await this.getFinalResults(filters);

            const stats = {
                total_students: finalResults.length,
                students_with_both_semesters: finalResults.filter(r => r.hasBothSemesters).length,
                students_with_incomplete_data: finalResults.filter(r => !r.hasBothSemesters).length,
                average_final_total: 0,
                grade_distribution: {
                    excellent: 0,
                    very_good: 0,
                    good: 0,
                    acceptable: 0,
                    weak: 0
                }
            };

            if (finalResults.length > 0) {
                const totalSum = finalResults.reduce((sum, r) => sum + r.finalTotal, 0);
                stats.average_final_total = totalSum / finalResults.length;

                // حساب توزيع الدرجات (يمكن تحسينه لاحقاً)
                finalResults.forEach(result => {
                    const percentage = (result.finalTotal / 200) * 100; // افتراض أن المجموع الكلي 200

                    if (percentage >= 90) stats.grade_distribution.excellent++;
                    else if (percentage >= 80) stats.grade_distribution.very_good++;
                    else if (percentage >= 70) stats.grade_distribution.good++;
                    else if (percentage >= 60) stats.grade_distribution.acceptable++;
                    else stats.grade_distribution.weak++;
                });
            }

            return stats;

        } catch (error) {
            console.error('خطأ في حساب إحصائيات النتائج النهائية:', error);
            throw error;
        }
    }
}

// إنشاء مثيل من مدير قاعدة البيانات
const dbManager = new DatabaseManager();
