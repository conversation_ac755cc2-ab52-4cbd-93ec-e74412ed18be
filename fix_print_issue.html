<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة طباعة النتائج النهائية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        .fix-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .fix-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .fix-section h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .fix-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #219a52;
        }
        .btn-warning {
            background: #f39c12;
        }
        .btn-warning:hover {
            background: #e67e22;
        }
        .btn-danger {
            background: #e74c3c;
        }
        .btn-danger:hover {
            background: #c0392b;
        }
        .step-by-step {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .step-by-step h4 {
            color: #2980b9;
            margin-bottom: 10px;
        }
        .step-by-step ol {
            margin: 0;
            padding-right: 20px;
        }
        .step-by-step li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <h1>إصلاح مشكلة طباعة النتائج النهائية</h1>
        
        <div class="step-by-step">
            <h4>خطوات الحل:</h4>
            <ol>
                <li>إعداد بيانات تجريبية (طلاب ودرجات)</li>
                <li>تحميل النتائج النهائية</li>
                <li>اختبار الطباعة</li>
                <li>فتح التطبيق الرئيسي واختبار الطباعة</li>
            </ol>
        </div>

        <div class="fix-section">
            <h3>1. إعداد البيانات التجريبية</h3>
            <button class="btn-success" onclick="setupCompleteTestData()">إعداد بيانات كاملة</button>
            <div id="setup-result" class="fix-result"></div>
        </div>

        <div class="fix-section">
            <h3>2. تحميل النتائج النهائية</h3>
            <button onclick="loadAndTestResults()">تحميل واختبار النتائج</button>
            <div id="load-result" class="fix-result"></div>
        </div>

        <div class="fix-section">
            <h3>3. اختبار الطباعة المباشر</h3>
            <button onclick="directPrintTest()">اختبار طباعة مباشر</button>
            <button class="btn-warning" onclick="debugPrintContent()">تشخيص محتوى الطباعة</button>
            <div id="print-result" class="fix-result"></div>
        </div>

        <div class="fix-section">
            <h3>4. فتح التطبيق الرئيسي</h3>
            <button class="btn-success" onclick="openMainAppWithInstructions()">فتح التطبيق مع التعليمات</button>
            <div id="app-result" class="fix-result"></div>
        </div>

        <div class="fix-section">
            <h3>5. إعادة تعيين النظام (إذا لزم الأمر)</h3>
            <button class="btn-danger" onclick="resetAndFix()">إعادة تعيين وإصلاح</button>
            <div id="reset-result" class="fix-result"></div>
        </div>
    </div>

    <!-- تضمين ملفات JavaScript -->
    <script src="js/database.js"></script>
    <script src="js/final-results.js"></script>
    <script>
        let dbManager;
        let finalResultsManager;
        
        window.addEventListener('DOMContentLoaded', async () => {
            try {
                dbManager = new DatabaseManager();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                finalResultsManager = new FinalResultsManager();
                
                document.getElementById('setup-result').textContent = '✓ تم تحميل النظام بنجاح - جاهز للإصلاح';
                document.getElementById('setup-result').className = 'fix-result success';
            } catch (error) {
                document.getElementById('setup-result').textContent = '✗ خطأ في تحميل النظام: ' + error.message;
                document.getElementById('setup-result').className = 'fix-result error';
            }
        });

        async function setupCompleteTestData() {
            const result = document.getElementById('setup-result');
            try {
                result.textContent = 'جاري إعداد البيانات...';
                result.className = 'fix-result info';

                // إضافة طلاب تجريبيين
                const students = [
                    { student_number: '1001', name: 'أحمد محمد علي', grade: '1', section: 'أ' },
                    { student_number: '1002', name: 'فاطمة أحمد سالم', grade: '1', section: 'أ' },
                    { student_number: '1003', name: 'محمد علي حسن', grade: '1', section: 'أ' },
                    { student_number: '1004', name: 'نور الدين خالد', grade: '1', section: 'ب' },
                    { student_number: '1005', name: 'سارة عبدالله', grade: '1', section: 'ب' },
                ];

                let addedStudents = 0;
                for (const student of students) {
                    try {
                        await dbManager.addStudent(student);
                        addedStudents++;
                    } catch (error) {
                        // تجاهل خطأ الطالب المكرر
                    }
                }

                // إضافة درجات تجريبية للفصل الأول والثاني
                const allStudents = await dbManager.getStudents();
                let addedGrades = 0;

                for (const student of allStudents) {
                    // درجات الفصل الأول
                    try {
                        await dbManager.saveGrades({
                            student_id: student.id,
                            academic_year: '2024-2025',
                            semester: 1,
                            grade_level: student.grade,
                            component_name: 'اختبار نهاية الفصل',
                            grade: Math.floor(Math.random() * 30) + 70, // درجة عشوائية بين 70-100
                            total: Math.floor(Math.random() * 30) + 70
                        });
                        addedGrades++;
                    } catch (error) {
                        // تجاهل الأخطاء
                    }

                    // درجات الفصل الثاني
                    try {
                        await dbManager.saveGrades({
                            student_id: student.id,
                            academic_year: '2024-2025',
                            semester: 2,
                            grade_level: student.grade,
                            component_name: 'اختبار نهاية الفصل',
                            grade: Math.floor(Math.random() * 30) + 70, // درجة عشوائية بين 70-100
                            total: Math.floor(Math.random() * 30) + 70
                        });
                        addedGrades++;
                    } catch (error) {
                        // تجاهل الأخطاء
                    }
                }

                result.textContent = `✓ تم إعداد البيانات بنجاح!\nالطلاب المضافون: ${addedStudents}\nالدرجات المضافة: ${addedGrades}\nإجمالي الطلاب: ${allStudents.length}`;
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في إعداد البيانات: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        async function loadAndTestResults() {
            const result = document.getElementById('load-result');
            try {
                result.textContent = 'جاري تحميل النتائج...';
                result.className = 'fix-result info';

                // تحميل الطلاب
                const students = await dbManager.getStudents({ grade: '1', section: 'أ' });
                
                if (students.length === 0) {
                    result.textContent = '⚠ لا توجد طلاب. يرجى إعداد البيانات أولاً';
                    result.className = 'fix-result warning';
                    return;
                }

                // تحميل درجات الفصل الأول
                const semester1Grades = await dbManager.getGrades({
                    academic_year: '2024-2025',
                    semester: 1,
                    grade_level: '1'
                });

                // تحميل درجات الفصل الثاني
                const semester2Grades = await dbManager.getGrades({
                    academic_year: '2024-2025',
                    semester: 2,
                    grade_level: '1'
                });

                // حساب النتائج النهائية
                const finalResults = finalResultsManager.calculateFinalResults(students, semester1Grades, semester2Grades);
                
                // تعيين النتائج في المدير
                finalResultsManager.currentResults = finalResults;

                result.textContent = `✓ تم تحميل النتائج بنجاح!\nعدد الطلاب: ${students.length}\nدرجات الفصل الأول: ${semester1Grades.length}\nدرجات الفصل الثاني: ${semester2Grades.length}\nالنتائج النهائية: ${finalResults.length}`;
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في تحميل النتائج: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        function directPrintTest() {
            const result = document.getElementById('print-result');
            try {
                if (!finalResultsManager.currentResults || finalResultsManager.currentResults.length === 0) {
                    result.textContent = '⚠ لا توجد نتائج محملة. يرجى تحميل النتائج أولاً';
                    result.className = 'fix-result warning';
                    return;
                }

                // اختبار الطباعة المباشر
                finalResultsManager.printResults('color');
                
                result.textContent = '✓ تم فتح نافذة الطباعة. تحقق من النافذة الجديدة\nإذا كانت فارغة، استخدم "تشخيص محتوى الطباعة"';
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في الطباعة المباشرة: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        function debugPrintContent() {
            const result = document.getElementById('print-result');
            try {
                if (!finalResultsManager.currentResults || finalResultsManager.currentResults.length === 0) {
                    result.textContent = '⚠ لا توجد نتائج محملة. يرجى تحميل النتائج أولاً';
                    result.className = 'fix-result warning';
                    return;
                }

                // إنشاء محتوى الطباعة
                const printContent = finalResultsManager.generateFinalResultsPrintContent('color');
                
                // فتح نافذة جديدة مع المحتوى
                const debugWindow = window.open('', '_blank');
                debugWindow.document.write(printContent);
                debugWindow.document.close();
                
                result.textContent = `✓ تم فتح نافذة التشخيص\nطول المحتوى: ${printContent.length} حرف\nيحتوي على جدول: ${printContent.includes('<table>') ? 'نعم' : 'لا'}\nيحتوي على بيانات: ${printContent.includes('student-name') ? 'نعم' : 'لا'}`;
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في تشخيص المحتوى: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        function openMainAppWithInstructions() {
            const result = document.getElementById('app-result');
            try {
                window.open('index.html', '_blank');
                
                result.textContent = `✓ تم فتح التطبيق الرئيسي

تعليمات الاستخدام:
1. انتقل إلى "النتائج النهائية"
2. اختر العام الدراسي: 2024-2025
3. اختر الصف: الصف الأول
4. اختر الشعبة: أ
5. اضغط "تحميل النتائج"
6. اضغط "خيارات الطباعة"
7. اختر نوع الطباعة المطلوب`;
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في فتح التطبيق: ' + error.message;
                result.className = 'fix-result error';
            }
        }

        async function resetAndFix() {
            if (!confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم مسح جميع البيانات.')) {
                return;
            }

            const result = document.getElementById('reset-result');
            try {
                result.textContent = 'جاري إعادة التعيين...';
                result.className = 'fix-result info';

                // مسح البيانات الموجودة
                const students = await dbManager.getStudents();
                for (const student of students) {
                    await dbManager.deleteStudent(student.id);
                }

                // إعادة إعداد البيانات
                await setupCompleteTestData();
                await loadAndTestResults();
                
                result.textContent = '✓ تم إعادة تعيين النظام وإصلاحه بنجاح';
                result.className = 'fix-result success';
                
            } catch (error) {
                result.textContent = '✗ خطأ في إعادة التعيين: ' + error.message;
                result.className = 'fix-result error';
            }
        }
    </script>
</body>
</html>
