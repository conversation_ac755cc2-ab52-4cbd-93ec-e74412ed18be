// إدارة الدرجات
class GradesManager {
    constructor() {
        this.currentGradeLevel = null;
        this.currentStudents = [];
        this.gradeStructures = this.initGradeStructures();
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // أحداث اختيار الصف والشعبة
        document.getElementById('grade-select')?.addEventListener('change', () => this.onGradeChange());
        document.getElementById('section-select')?.addEventListener('change', () => this.onSectionChange());
    }

    initGradeStructures() {
        return {
            // الصفوف 1-4 (المجموع من 50)
            elementary: {
                grades: [1, 2, 3, 4],
                components: [
                    { name: 'oral_work_1', label: 'الأعمال الشفوية (الفترة الأولى)', max: 10 },
                    { name: 'oral_work_2', label: 'الأعمال الشفوية (الفترة الثانية)', max: 10 },
                    { name: 'practical_activities_1', label: 'الأنشطة العملية (الفترة الأولى)', max: 10 },
                    { name: 'practical_activities_2', label: 'الأنشطة العملية (الفترة الثانية)', max: 10 },
                    { name: 'project', label: 'المشروع', max: 10 }
                ],
                total: 50,
                gradeScale: [
                    { min: 47.25, level: '1', description: 'ممتاز' },
                    { min: 44.75, level: '2', description: 'جيد جداً' },
                    { min: 39.75, level: '3', description: 'جيد' },
                    { min: 34.75, level: '4', description: 'مقبول' },
                    { min: 29.75, level: '5', description: 'ضعيف' },
                    { min: 24.75, level: '6', description: 'ضعيف جداً' },
                    { min: 0, level: '7', description: 'راسب' }
                ]
            },
            // الصفوف 5-10 (المجموع من 100)
            intermediate: {
                grades: [5, 6, 7, 8, 9, 10],
                components: [
                    { name: 'oral_work_1', label: 'الأعمال الشفوية (الفترة الأولى)', max: 10 },
                    { name: 'oral_work_2', label: 'الأعمال الشفوية (الفترة الثانية)', max: 10 },
                    { name: 'practical_activities_1', label: 'الأنشطة العملية (الفترة الأولى)', max: 20 },
                    { name: 'practical_activities_2', label: 'الأنشطة العملية (الفترة الثانية)', max: 20 },
                    { name: 'project', label: 'المشروع', max: 20 },
                    { name: 'short_test', label: 'الاختبار القصير', max: 20 }
                ],
                total: 100,
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز' },
                    { min: 80, level: 'ب', description: 'جيد جداً' },
                    { min: 70, level: 'ج', description: 'جيد' },
                    { min: 60, level: 'د', description: 'مقبول' },
                    { min: 0, level: 'هـ', description: 'راسب' }
                ]
            },
            // الصفوف 11-12 (المجموع من 100)
            secondary: {
                grades: [11, 12],
                components: [
                    { name: 'oral_work_1', label: 'الأعمال الشفوية (الفترة الأولى)', max: 5 },
                    { name: 'oral_work_2', label: 'الأعمال الشفوية (الفترة الثانية)', max: 5 },
                    { name: 'practical_activities_1', label: 'الأنشطة العملية (الفترة الأولى)', max: 20 },
                    { name: 'practical_activities_2', label: 'الأنشطة العملية (الفترة الثانية)', max: 20 },
                    { name: 'project', label: 'المشروع', max: 20 },
                    { name: 'short_test', label: 'الاختبار القصير', max: 20 },
                    { name: 'final_exam', label: 'الاختبار النهائي', max: 30 }
                ],
                total: 100,
                gradeScale: [
                    { min: 90, level: 'أ', description: 'ممتاز' },
                    { min: 80, level: 'ب', description: 'جيد جداً' },
                    { min: 70, level: 'ج', description: 'جيد' },
                    { min: 60, level: 'د', description: 'مقبول' },
                    { min: 0, level: 'هـ', description: 'راسب' }
                ]
            }
        };
    }

    onGradeChange() {
        const gradeSelect = document.getElementById('grade-select');
        const grade = parseInt(gradeSelect.value);
        
        if (grade) {
            this.currentGradeLevel = grade;
            this.updateGradeStructure();
        }
    }

    onSectionChange() {
        // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
    }

    updateGradeStructure() {
        const grade = this.currentGradeLevel;
        let structure;

        if (grade >= 1 && grade <= 4) {
            structure = this.gradeStructures.elementary;
        } else if (grade >= 5 && grade <= 10) {
            structure = this.gradeStructures.intermediate;
        } else if (grade >= 11 && grade <= 12) {
            structure = this.gradeStructures.secondary;
        }

        this.currentStructure = structure;
    }

    async loadGradeSheet() {
        const academicYear = document.getElementById('academic-year').value;
        const semester = document.getElementById('semester').value;
        const grade = document.getElementById('grade-select').value;
        const section = document.getElementById('section-select').value;

        if (!academicYear || !semester || !grade || !section) {
            showNotification('يرجى اختيار جميع الحقول المطلوبة', 'warning');
            return;
        }

        try {
            // تحميل الطلاب
            const students = await dbManager.getStudents({ grade: grade, section: section });
            
            if (students.length === 0) {
                showNotification('لا توجد طلاب في هذا الصف والشعبة', 'warning');
                return;
            }

            this.currentStudents = students;
            this.currentGradeLevel = parseInt(grade);
            this.updateGradeStructure();

            // تحميل الدرجات الموجودة
            const existingGrades = await dbManager.getGrades({
                academic_year: academicYear,
                semester: semester,
                grade_level: grade
            });

            this.renderGradeSheet(existingGrades);

        } catch (error) {
            console.error('خطأ في تحميل جدول الدرجات:', error);
            showNotification('خطأ في تحميل جدول الدرجات', 'error');
        }
    }

    renderGradeSheet(existingGrades = []) {
        const container = document.getElementById('grade-sheet-container');
        if (!container) return;

        const structure = this.currentStructure;
        if (!structure) return;

        // إنشاء جدول الدرجات
        let tableHTML = `
            <div class="grade-sheet-header">
                <h3>جدول الدرجات - الصف ${this.currentGradeLevel}</h3>
                <div class="grade-sheet-info">
                    <span>العام الدراسي: ${document.getElementById('academic-year').value}</span>
                    <span>الفصل: ${document.getElementById('semester').value}</span>
                    <span>الشعبة: ${document.getElementById('section-select').value}</span>
                </div>
            </div>
            <div class="grade-table-container">
                <table class="grade-table">
                    <thead>
                        <tr>
                            <th rowspan="2">الرقم</th>
                            <th rowspan="2">اسم الطالب</th>
        `;

        // إضافة رؤوس الأعمدة للمكونات
        structure.components.forEach(component => {
            tableHTML += `<th>${component.label}<br><small>(${component.max} درجة)</small></th>`;
        });

        tableHTML += `
                            <th rowspan="2">المجموع<br><small>(${structure.total})</small></th>
                            <th rowspan="2">المستوى</th>
                            <th rowspan="2">العبارة الوصفية</th>
                            <th rowspan="2">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        // إضافة صفوف الطلاب
        this.currentStudents.forEach((student, index) => {
            const studentGrades = existingGrades.find(g => g.student_id === student.id) || {};
            
            tableHTML += `
                <tr data-student-id="${student.id}">
                    <td>${student.student_number}</td>
                    <td>${student.name}</td>
            `;

            // إضافة خلايا إدخال الدرجات
            structure.components.forEach(component => {
                const value = studentGrades[component.name] || '';
                tableHTML += `
                    <td>
                        <input type="number" 
                               class="grade-input" 
                               data-component="${component.name}"
                               data-max="${component.max}"
                               value="${value}"
                               min="0" 
                               max="${component.max}"
                               step="0.25">
                    </td>
                `;
            });

            const total = this.calculateTotal(studentGrades, structure);
            const gradeInfo = this.getGradeInfo(total, structure);

            tableHTML += `
                    <td class="total-cell">${total.toFixed(2)}</td>
                    <td class="level-cell">${gradeInfo.level}</td>
                    <td class="description-cell">${gradeInfo.description}</td>
                    <td>
                        <button class="btn btn-sm btn-success" onclick="gradesManager.saveStudentGrades(${student.id})">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
            <div class="grade-sheet-actions no-print">
                <button class="btn btn-primary" onclick="gradesManager.saveAllGrades()">
                    <i class="fas fa-save"></i> حفظ جميع الدرجات
                </button>
                <button class="btn btn-info" onclick="gradesManager.calculateAllTotals()">
                    <i class="fas fa-calculator"></i> حساب المجاميع
                </button>
                <button class="btn btn-success" onclick="gradesManager.exportGradeSheet()">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </button>
            </div>
        `;

        container.innerHTML = tableHTML;

        // ربط أحداث إدخال الدرجات
        this.bindGradeInputEvents();
    }

    bindGradeInputEvents() {
        const gradeInputs = document.querySelectorAll('.grade-input');
        gradeInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.validateGradeInput(e.target);
                this.updateStudentTotal(e.target.closest('tr'));
            });

            input.addEventListener('blur', (e) => {
                this.formatGradeInput(e.target);
            });
        });
    }

    validateGradeInput(input) {
        const max = parseFloat(input.dataset.max);
        const value = parseFloat(input.value);

        if (value > max) {
            input.value = max;
            showNotification(`الدرجة القصوى هي ${max}`, 'warning');
        }

        if (value < 0) {
            input.value = 0;
        }
    }

    formatGradeInput(input) {
        if (input.value) {
            input.value = parseFloat(input.value).toFixed(2);
        }
    }

    updateStudentTotal(row) {
        const inputs = row.querySelectorAll('.grade-input');
        let total = 0;

        inputs.forEach(input => {
            const value = parseFloat(input.value) || 0;
            total += value;
        });

        const totalCell = row.querySelector('.total-cell');
        const levelCell = row.querySelector('.level-cell');
        const descriptionCell = row.querySelector('.description-cell');

        totalCell.textContent = total.toFixed(2);

        const gradeInfo = this.getGradeInfo(total, this.currentStructure);
        levelCell.textContent = gradeInfo.level;
        descriptionCell.textContent = gradeInfo.description;

        // تلوين الخلايا حسب المستوى
        this.applyGradeColors(row, gradeInfo.level);
    }

    calculateTotal(grades, structure) {
        let total = 0;
        structure.components.forEach(component => {
            total += parseFloat(grades[component.name]) || 0;
        });
        return total;
    }

    getGradeInfo(total, structure) {
        for (const grade of structure.gradeScale) {
            if (total >= grade.min) {
                return grade;
            }
        }
        return structure.gradeScale[structure.gradeScale.length - 1];
    }

    applyGradeColors(row, level) {
        // إزالة الألوان السابقة
        row.classList.remove('grade-excellent', 'grade-very-good', 'grade-good', 'grade-acceptable', 'grade-weak');

        // تطبيق اللون المناسب
        if (level === 'أ' || level === '1') {
            row.classList.add('grade-excellent');
        } else if (level === 'ب' || level === '2') {
            row.classList.add('grade-very-good');
        } else if (level === 'ج' || level === '3') {
            row.classList.add('grade-good');
        } else if (level === 'د' || level === '4') {
            row.classList.add('grade-acceptable');
        } else {
            row.classList.add('grade-weak');
        }
    }

    calculateAllTotals() {
        const rows = document.querySelectorAll('#grade-sheet-container tbody tr');
        rows.forEach(row => {
            this.updateStudentTotal(row);
        });
        showNotification('تم حساب جميع المجاميع', 'success');
    }

    async saveStudentGrades(studentId) {
        try {
            const row = document.querySelector(`tr[data-student-id="${studentId}"]`);
            const gradeData = this.collectGradeData(row, studentId);
            
            await dbManager.saveGrades(gradeData);
            showNotification('تم حفظ درجات الطالب بنجاح', 'success');
            
        } catch (error) {
            console.error('خطأ في حفظ الدرجات:', error);
            showNotification('خطأ في حفظ الدرجات', 'error');
        }
    }

    async saveAllGrades() {
        try {
            const rows = document.querySelectorAll('#grade-sheet-container tbody tr');
            let successCount = 0;
            let errorCount = 0;

            for (const row of rows) {
                try {
                    const studentId = parseInt(row.dataset.studentId);
                    const gradeData = this.collectGradeData(row, studentId);
                    await dbManager.saveGrades(gradeData);
                    successCount++;
                } catch (error) {
                    console.error('خطأ في حفظ درجات الطالب:', error);
                    errorCount++;
                }
            }

            showNotification(
                `تم حفظ درجات ${successCount} طالب. فشل في حفظ ${errorCount} طالب.`,
                successCount > 0 ? 'success' : 'error'
            );

        } catch (error) {
            console.error('خطأ في حفظ جميع الدرجات:', error);
            showNotification('خطأ في حفظ الدرجات', 'error');
        }
    }

    collectGradeData(row, studentId) {
        const inputs = row.querySelectorAll('.grade-input');
        const gradeData = {
            student_id: studentId,
            academic_year: document.getElementById('academic-year').value,
            semester: parseInt(document.getElementById('semester').value),
            grade_level: this.currentGradeLevel
        };

        // جمع درجات المكونات
        inputs.forEach(input => {
            const component = input.dataset.component;
            gradeData[component] = parseFloat(input.value) || 0;
        });

        // حساب المجموع والمستوى
        gradeData.total = this.calculateTotal(gradeData, this.currentStructure);
        const gradeInfo = this.getGradeInfo(gradeData.total, this.currentStructure);
        gradeData.level = gradeInfo.level;
        gradeData.descriptive_phrase = gradeInfo.description;

        return gradeData;
    }

    exportGradeSheet() {
        const table = document.querySelector('.grade-table');
        if (!table) {
            showNotification('لا يوجد جدول درجات للتصدير', 'warning');
            return;
        }

        // تحويل الجدول إلى Excel
        const wb = XLSX.utils.table_to_book(table, { sheet: 'الدرجات' });
        const fileName = `درجات_الصف_${this.currentGradeLevel}_${document.getElementById('academic-year').value}.xlsx`;
        XLSX.writeFile(wb, fileName);
        
        showNotification('تم تصدير جدول الدرجات بنجاح', 'success');
    }

    // دوال الطباعة الاحترافية
    printGradeSheet(mode = 'color') {
        if (!this.currentStudents.length) {
            showNotification('لا توجد بيانات للطباعة', 'warning');
            return;
        }

        // إنشاء نافذة طباعة جديدة
        const printWindow = window.open('', '_blank');
        const printContent = this.generatePrintContent(mode);

        printWindow.document.write(printContent);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            printWindow.focus();
            printWindow.print();
        };
    }

    generatePrintContent(mode) {
        const settings = app.settings || {};
        const currentDate = new Date();
        const formattedDate = currentDate.toLocaleDateString('ar-EG');
        const formattedTime = currentDate.toLocaleTimeString('ar-EG', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const gradeLevel = this.currentGradeLevel;
        const section = document.getElementById('section-select').value;
        const academicYear = document.getElementById('academic-year').value;
        const semester = document.getElementById('semester').value;
        const semesterText = semester === '1' ? 'الفصل الأول' : 'الفصل الثاني';

        // إنشاء محتوى الطباعة
        let printHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>جدول الدرجات - الصف ${gradeLevel} - الشعبة ${section}</title>
            <style>
                ${this.getPrintStyles(mode)}
            </style>
        </head>
        <body>
            <div class="print-container">
                <!-- رأس الصفحة الاحترافي -->
                <header class="print-header">
                    <div class="header-top">
                        <div class="ministry-info">
                            <h1>${settings.ministry_name || 'وزارة التربية والتعليم'}</h1>
                            <h2>${settings.directorate_name || 'مديرية التربية والتعليم'}</h2>
                            <h3>${settings.school_name || 'مدرسة تقنية المعلومات'}</h3>
                        </div>
                        <div class="date-time">
                            <div class="date">التاريخ: ${formattedDate}</div>
                            <div class="time">الوقت: ${formattedTime}</div>
                        </div>
                    </div>
                    <div class="document-title">
                        <h2>جدول درجات مادة تقنية المعلومات</h2>
                        <div class="document-info">
                            <span>الصف: ${gradeLevel}</span>
                            <span>الشعبة: ${section}</span>
                            <span>${semesterText}</span>
                            <span>العام الدراسي: ${academicYear}</span>
                        </div>
                    </div>
                </header>

                <!-- محتوى الجدول -->
                <main class="print-content">
                    ${this.generatePrintTable(mode)}
                </main>

                <!-- تذييل الصفحة -->
                <footer class="print-footer">
                    <div class="signatures">
                        <div class="signature-item">
                            <div class="signature-line"></div>
                            <div class="signature-label">المعلم: ${settings.teacher_name || '........................'}</div>
                        </div>
                        <div class="signature-item">
                            <div class="signature-line"></div>
                            <div class="signature-label">المشرف: ${settings.supervisor_name || '........................'}</div>
                        </div>
                        <div class="signature-item">
                            <div class="signature-line"></div>
                            <div class="signature-label">مدير المدرسة: ${settings.principal_name || '........................'}</div>
                        </div>
                    </div>
                </footer>
            </div>
        </body>
        </html>
        `;

        return printHTML;
    }

    generatePrintTable(mode) {
        const structure = this.currentStructure;
        if (!structure) return '';

        let tableHTML = `
            <table class="grade-table">
                <thead>
                    <tr>
                        <th rowspan="2" class="student-number">الرقم</th>
                        <th rowspan="2" class="student-name">اسم الطالب</th>
        `;

        // إضافة رؤوس المكونات
        structure.components.forEach(component => {
            tableHTML += `<th class="component-header">${component.name}<br><small>(${component.max})</small></th>`;
        });

        tableHTML += `
                        <th rowspan="2" class="total-header">المجموع<br><small>(${structure.total})</small></th>
                        <th rowspan="2" class="level-header">المستوى</th>
                        <th rowspan="2" class="description-header">التقدير</th>
                    </tr>
                </thead>
                <tbody>
        `;

        // إضافة صفوف الطلاب
        this.currentStudents.forEach((student, index) => {
            const row = document.querySelector(`tr[data-student-id="${student.id}"]`);
            if (!row) return;

            const total = parseFloat(row.querySelector('.total-cell')?.textContent || '0');
            const level = row.querySelector('.level-cell')?.textContent || '';
            const description = row.querySelector('.description-cell')?.textContent || '';

            const rowClass = this.getGradeRowClass(level, mode);

            tableHTML += `
                <tr class="${rowClass}">
                    <td class="student-number">${student.student_number}</td>
                    <td class="student-name">${student.name}</td>
            `;

            // إضافة درجات المكونات
            structure.components.forEach(component => {
                const input = row.querySelector(`input[data-component="${component.name}"]`);
                const value = input ? parseFloat(input.value || 0).toFixed(2) : '0.00';
                tableHTML += `<td class="grade-cell">${value}</td>`;
            });

            tableHTML += `
                    <td class="total-cell">${total.toFixed(2)}</td>
                    <td class="level-cell">${level}</td>
                    <td class="description-cell">${description}</td>
                </tr>
            `;
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        return tableHTML;
    }

    getGradeRowClass(level, mode) {
        if (mode === 'bw') return '';

        switch(level) {
            case 'أ': case '1': return 'grade-excellent';
            case 'ب': case '2': return 'grade-very-good';
            case 'ج': case '3': return 'grade-good';
            case 'د': case '4': return 'grade-acceptable';
            default: return 'grade-weak';
        }
    }

    getPrintStyles(mode) {
        const baseStyles = `
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            @page {
                size: A4 landscape;
                margin: 1.5cm 1cm;
                direction: rtl;
            }

            body {
                font-family: 'Noto Sans Arabic', 'Arial Unicode MS', Arial, sans-serif;
                font-size: 11pt;
                line-height: 1.3;
                color: #000;
                background: white;
                direction: rtl;
                text-align: right;
            }

            .print-container {
                width: 100%;
                max-width: 100%;
            }

            /* رأس الصفحة الاحترافي */
            .print-header {
                border-bottom: 3px solid #2c3e50;
                margin-bottom: 15px;
                padding-bottom: 10px;
            }

            .header-top {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 10px;
            }

            .ministry-info h1 {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 3px;
            }

            .ministry-info h2 {
                font-size: 14pt;
                font-weight: 600;
                color: #34495e;
                margin-bottom: 2px;
            }

            .ministry-info h3 {
                font-size: 12pt;
                font-weight: 500;
                color: #7f8c8d;
            }

            .date-time {
                text-align: left;
                font-size: 10pt;
                color: #7f8c8d;
                line-height: 1.4;
            }

            .document-title {
                text-align: center;
                border-top: 1px solid #bdc3c7;
                padding-top: 8px;
            }

            .document-title h2 {
                font-size: 14pt;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }

            .document-info {
                font-size: 10pt;
                color: #7f8c8d;
            }

            .document-info span {
                margin: 0 10px;
                padding: 2px 8px;
                background: #ecf0f1;
                border-radius: 3px;
            }

            /* جدول الدرجات */
            .grade-table {
                width: 100%;
                border-collapse: collapse;
                margin: 10px 0;
                font-size: 9pt;
            }

            .grade-table th,
            .grade-table td {
                border: 1px solid #34495e;
                padding: 4px 6px;
                text-align: center;
                vertical-align: middle;
            }

            .grade-table th {
                background: linear-gradient(135deg, #2c3e50, #34495e);
                color: white;
                font-weight: bold;
                font-size: 9pt;
            }

            .student-name {
                text-align: right !important;
                min-width: 120px;
                max-width: 150px;
            }

            .student-number {
                min-width: 40px;
            }

            .component-header {
                min-width: 50px;
                writing-mode: horizontal-tb;
            }

            .grade-cell {
                font-weight: 500;
                min-width: 45px;
            }

            .total-cell {
                font-weight: bold;
                background: #ecf0f1 !important;
                min-width: 60px;
            }

            .level-cell {
                font-weight: bold;
                font-size: 10pt;
                min-width: 40px;
            }

            .description-cell {
                font-weight: 500;
                min-width: 80px;
            }

            /* تذييل الصفحة */
            .print-footer {
                margin-top: 20px;
                border-top: 2px solid #2c3e50;
                padding-top: 15px;
            }

            .signatures {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                gap: 20px;
            }

            .signature-item {
                flex: 1;
                text-align: center;
            }

            .signature-line {
                width: 100%;
                height: 1px;
                background: #2c3e50;
                margin-bottom: 5px;
            }

            .signature-label {
                font-size: 10pt;
                font-weight: 500;
                color: #2c3e50;
            }
        `;

        // إضافة ألوان المستويات حسب نوع الطباعة
        let colorStyles = '';
        if (mode === 'color' || mode === 'official') {
            colorStyles = `
                .grade-excellent {
                    background-color: #d4edda !important;
                    color: #155724 !important;
                }
                .grade-very-good {
                    background-color: #d1ecf1 !important;
                    color: #0c5460 !important;
                }
                .grade-good {
                    background-color: #fff3cd !important;
                    color: #856404 !important;
                }
                .grade-acceptable {
                    background-color: #ffeaa7 !important;
                    color: #6c5ce7 !important;
                }
                .grade-weak {
                    background-color: #f8d7da !important;
                    color: #721c24 !important;
                }
            `;
        }

        return baseStyles + colorStyles;
    }
}

// إنشاء مثيل من مدير الدرجات
const gradesManager = new GradesManager();

// دوال مساعدة
function loadGradeSheet() {
    gradesManager.loadGradeSheet();
}

// دوال مساعدة للطباعة
function toggleGradePrintDropdown() {
    const dropdown = document.getElementById('grade-print-dropdown');
    dropdown.classList.toggle('show');
}

// إغلاق القوائم المنسدلة عند النقر خارجها
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('grade-print-dropdown');
    const button = event.target.closest('.dropdown-toggle');

    if (!button && dropdown) {
        dropdown.classList.remove('show');
    }
});
