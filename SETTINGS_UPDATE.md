# تحديث شاشة الإعدادات

## التحديثات المضافة

تم إضافة الحقول التالية إلى شاشة الإعدادات:

### البيانات الإدارية الجديدة:
1. **الوزارة** - حقل لإدخال اسم الوزارة (افتراضي: وزارة التربية والتعليم)
2. **المديرية** - حقل لإدخال اسم المديرية (افتراضي: مديرية التربية والتعليم)
3. **اسم مدير المدرسة** - حقل لإدخال اسم مدير المدرسة
4. **اسم المشرف** - حقل لإدخال اسم المشرف
5. **اسم المعلم** - حقل لإدخال اسم المعلم

### زر الحفظ:
- تم إضافة زر "حفظ الإعدادات" لحفظ جميع البيانات دفعة واحدة
- يعرض رسالة تأكيد عند نجاح الحفظ
- يعرض رسالة خطأ في حالة فشل الحفظ

## الملفات المحدثة:

### 1. index.html
- إضافة قسم "البيانات الإدارية" الجديد
- إضافة الحقول الخمسة الجديدة
- إضافة زر الحفظ

### 2. js/app.js
- تحديث دالة `loadSettings()` لتشمل الحقول الجديدة
- تحديث دالة `updateUIWithSettings()` لتحديث الحقول الجديدة
- تحديث دالة `bindSettingsEvents()` لربط زر الحفظ
- إضافة دالة `saveAllSettings()` لحفظ جميع الإعدادات

### 3. js/database.js
- تحديث دالة `insertDefaultData()` لتشمل القيم الافتراضية للحقول الجديدة

### 4. css/styles.css
- إضافة تنسيقات للحقول الجديدة
- تحسين مظهر زر الحفظ
- إضافة تأثيرات التركيز للحقول

## كيفية الاستخدام:

1. افتح التطبيق
2. انتقل إلى قسم "الإعدادات"
3. ستجد قسم "البيانات الإدارية" الجديد
4. أدخل البيانات المطلوبة في الحقول
5. اضغط على زر "حفظ الإعدادات"
6. ستظهر رسالة تأكيد عند نجاح الحفظ

## المميزات:

- **حفظ تلقائي**: يتم حفظ البيانات في قاعدة البيانات المحلية
- **واجهة سهلة**: تصميم بسيط ومفهوم
- **رسائل التأكيد**: تأكيد نجاح أو فشل العمليات
- **قيم افتراضية**: قيم افتراضية مناسبة للحقول
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات

## ملاحظات تقنية:

- يتم حفظ البيانات في IndexedDB
- الحقول تدعم النص العربي
- التصميم متوافق مع باقي أجزاء التطبيق
- يمكن تصدير واستيراد الإعدادات مع النسخ الاحتياطية
